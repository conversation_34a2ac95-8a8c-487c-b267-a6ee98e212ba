# Table Checkout Data Cleanup Test

## Problem Description
When a table is checked out (unseated), the `userOrdersActions` and `orderHistoryDetail` data from the previous customer session remains visible to the next customer.

## Root Cause Analysis
1. **Data Flow**: 
   - `selectedTableOrders` is populated by `listenToSelectedOutletTableIdChanges()` function
   - This data is filtered to exclude `ORDER_COMPLETED` status orders
   - `userOrdersActions` in `OrderNotificationListScreen.js` is derived from `selectedTableOrders`
   - `toRenderOrders` in `OrderHistoryDetail.js` and `OrderHistoryDetailTakeaway.js` are also derived from `selectedTableOrders`

2. **Issue**: 
   - When table is unseated (`seated <= 0`), the system redirects users but doesn't clear the cached data
   - Orders might not be properly marked as `ORDER_COMPLETED` during checkout
   - Data persists across customer sessions

## Solution Implemented

### 1. Modified `src/util/commonFuncs.js`
Added data cleanup when table is detected as unseated:

```javascript
// In listenToSelectedOutletTableIdChanges function
if (record.seated <= 0) {
    global.errorMsg = 'Table is already unseated (CF-01/CF-02).';

    // Clear table-related data when table is unseated
    CommonStore.update(s => {
        s.selectedTableOrders = [];
    });

    global.linkToFunc && global.linkToFunc(`${prefix}/scan`);
}
```

### 2. Modified `src/screen/OrderNotificationListScreen.js`
Added monitoring of table status and cleanup of local state:

```javascript
const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);

// Clear data when table is unseated
useEffect(() => {
    if (selectedOutletTablePax <= 0) {
        setUserOrdersDineIn([]);
        setUserOrdersDineInActions([]);
        setUserOrdersTakeawayActions([]);
        setUserOrdersActions([]);
    }
}, [selectedOutletTablePax]);
```

### 3. Modified `src/screen/OrderHistoryDetail.js`
Added similar cleanup logic:

```javascript
const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);

// Clear data when table is unseated
useEffect(() => {
    if (selectedOutletTablePax <= 0) {
        setToRenderOrders([]);
        setToApproveOrders([]);
        setSelectedTableOtherPeoplePromoVoucherOrders([]);
        setSelectedTablePendingApprovalOrders([]);
    }
}, [selectedOutletTablePax]);
```

### 4. Modified `src/screen/OrderHistoryDetailTakeaway.js`
Added cleanup for takeaway orders:

```javascript
const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);

// Clear data when table is unseated
useEffect(() => {
    if (selectedOutletTablePax <= 0) {
        setToRenderOrders([]);
        setPendingApprovalOrders([]);
    }
}, [selectedOutletTablePax]);
```

## How It Works

1. **Table Status Monitoring**: The system monitors `selectedOutletTablePax` which reflects the table's seated status
2. **Automatic Cleanup**: When `selectedOutletTablePax <= 0` (table unseated), all relevant data is cleared
3. **Multi-Layer Protection**: Cleanup happens at both the global level (`commonFuncs.js`) and component level
4. **Real-time Updates**: Uses React's `useEffect` to respond immediately to table status changes

## Expected Behavior After Fix

1. When a table is checked out (unseated):
   - `selectedTableOrders` is cleared globally
   - `userOrdersActions` is cleared in OrderNotificationListScreen
   - `toRenderOrders` and related data is cleared in OrderHistoryDetail screens
   
2. Next customer scanning the same table will see:
   - Empty order history
   - No previous customer's actions or rejected items
   - Clean slate for their session

## Testing Recommendations

1. **Scenario 1**: Customer A orders items, some get rejected, then checks out
   - Verify: Customer B sees no previous actions or rejected items
   
2. **Scenario 2**: Customer A has active orders, then table gets unseated
   - Verify: Data is immediately cleared from all screens
   
3. **Scenario 3**: Multiple customers use same table in sequence
   - Verify: Each customer only sees their own data

## Files Modified
- `src/util/commonFuncs.js`
- `src/screen/OrderNotificationListScreen.js` 
- `src/screen/OrderHistoryDetail.js`
- `src/screen/OrderHistoryDetailTakeaway.js`
