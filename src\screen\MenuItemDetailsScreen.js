import React, { Component, useCallback, useEffect, useState, useRef } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Dimensions, ActivityIndicator,
  FlatList,
  useWindowDimensions,
  InteractionManager,
} from "react-native";
import moment from 'moment';
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import AntDesign from "react-native-vector-icons/AntDesign";
import Icon from "react-native-vector-icons/Ionicons";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import * as Cart from "../util/Cart";
import Styles from "../constant/Styles";
import Icons from "react-native-vector-icons/Feather";
import Entypo from "react-native-vector-icons/Entypo";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from "react-native-numeric-input";
import Draggable from "react-native-draggable";
import Icon1 from "react-native-vector-icons/Feather";
import { CommonStore } from "../store/commonStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { UserStore } from "../store/userStore";
import AsyncImage from "../components/asyncImage";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { prefix } from "../constant/env";
import { APP_TYPE, CHARGES_TYPE, ORDER_TYPE, ORDER_TYPE_SUB, PRODUCT_PRICE_TYPE, UNIT_TYPE, UNIT_TYPE_SHORT, WEB_ORDER_VARIANT_LAYOUT } from "../constant/common";
import { isMobile, parseValidPriceText } from "../util/commonFuncs";
import { DataStore } from "../store/dataStore";
import { TempStore } from "../store/tempStore";
import { ReactComponent as DefaultImage } from '../svg/DefaultImage.svg';
import { PaymentStore } from "../store/paymentStore";
import { idbDel, idbGet, idbSet, safelyExecuteIdb } from "../util/db";
import { TableStore } from "../store/tableStore";

import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";

import Checkbox from 'rc-checkbox';
import { ANALYTICS, ANALYTICS_PARSED } from "../constant/analytics";
import { logEvent } from "firebase/analytics";
import { useSafeAreaInsets } from 'react-native-safe-area-context';
/**
 * MenuItemDetailScreen
 * function
 * *select the quantity, size, add-on, variation and remarks of the item
 * 
 * route.params
 * *outletData: array of data of the current outlet
 * *menuItem: data of the current item
 * *orderType: type of current order (unused?)
 * *refresh: boolean value to determine if page need refreshing
 * *cartItem: data of items currently in cart
 */

// var quantity2;
var total;
// var options;
var refreshAction = null;

const MenuItemDetailsScreen = props => {
  const {
    navigation,
    route,
  } = props;

  // const { orderType, refresh } = route.params;
  // const outletDataParam = route.params.outletData;
  // const menuItemParam = route.params.menuItem;
  // const cartItemParam = route.params.cartItem;

  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();

  const linkTo = useLinkTo();

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={async () => {
        // props.navigation.goBack();

        const subdomain = await AsyncStorage.getItem('latestSubdomain');

        if (!subdomain) {
          linkTo && linkTo(`${prefix}/outlet/menu`);
        }
        else {
          linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
        }

        // linkTo(`${prefix}/outlet/menu`);
      }}>
        <View style={{
          marginLeft: 10,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-start',
        }}>
          <Icon
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{
            }}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Regular',
              lineHeight: 22,
              marginTop: -1,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity onPress={() => {
        // props.navigation.navigate('Profile')
      }} style={{
        opacity: 0,
      }}>
        <View style={{ marginRight: 15 }}>
          <Icon name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -1,
      }}>
        {selectedOutlet &&
          <Text
            style={{
              fontSize: 20,
              lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.mainTxtColor,
            }}
          >
            {selectedOutlet.name}
          </Text>
        }
      </View>
    ),
  });

  // const [menuItem, setMenuItem] = useState(menuItemParam);
  // const [totalState, setTotalState] = useState(menuItemParam.price);
  // const [totalState, setTotalState] = useState(0);

  const [remark, setRemark] = useState('');
  // const [outletData, setOutletData] = useState(outletDataParam);
  const [visible, setVisible] = useState(false);
  const [optional, setOptional] = useState(0);
  const [optional1, setOptional1] = useState(1);
  const [quantity1, setQuantity1] = useState(1);
  const [qty, setQty] = useState(null);
  const [detail, setDetail] = useState([]);
  // const [cartItem, setCartItem] = useState([]);
  const [choice, setChoice] = useState(null);
  // const [cartItem, setCartItem] = useState(cartItemParam);
  const [size, setSize] = useState("small");
  const [clicked, setClicked] = useState(1);
  const [clicked1, setClicked1] = useState(0);
  const [clicked2, setClicked2] = useState(0);
  const [cartIcon, setCartIcon] = useState(false);
  const [expandChoice, setExpandChoice] = useState(false);

  // const menuItemParam = route.params.menuItem;
  // const [menuItem, setMenuItem] = useState(menuItemParam);
  // const [totalState, setTotalState] = useState(menuItemParam.price);
  // const [quantity2, setQuantity2] = useState(cartItem ? cartItem.options : []);
  // const [options, setOptions] = useState(cartItem ? cartItem.quantity : quantity);

  const [quantity, setQuantity] = useState(1);

  const [totalPrice, setTotalPrice] = useState(0);
  const [addOnPrice, setAddOnPrice] = useState(0);

  //////////////////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [variablePrice, setVariablePrice] = useState('0.00');

  //////////////////////////////////////////////////////////////////////////////////////

  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const outletsItemAddOnDict = CommonStore.useState(s => s.outletsItemAddOnDict);
  const outletsItemAddOnChoiceDict = CommonStore.useState(s => s.outletsItemAddOnChoiceDict);
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
  const selectedOutletItemAddOn = CommonStore.useState(s => s.selectedOutletItemAddOn);
  const selectedOutletItemAddOnOi = CommonStore.useState(s => s.selectedOutletItemAddOnOi);
  const selectedOutletItemAddOnChoice = CommonStore.useState(s => s.selectedOutletItemAddOnChoice);

  const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);

  const cartItems = CommonStore.useState(s => s.cartItems);
  const cartItemChoices = CommonStore.useState(s => s.cartItemChoices);

  const onUpdatingCartItem = CommonStore.useState(s => s.onUpdatingCartItem);
  const userCart = CommonStore.useState(s => s.userCart);

  const [qualifiedLoyaltyStamps, setQualifiedLoyaltyStamps] = useState([]);
  const [qualifiedLoyaltyStampBuy, setQualifiedLoyaltyStampBuy] = useState({});

  const orderType = CommonStore.useState(s => s.orderType);

  const loyaltyStampBuyItemSkuDict = CommonStore.useState(s => s.loyaltyStampBuyItemSkuDict);
  const loyaltyStampBuyCategoryNameDict = CommonStore.useState(s => s.loyaltyStampBuyCategoryNameDict);
  const selectedOutletLoyaltyStamps = CommonStore.useState(s => s.selectedOutletLoyaltyStamps);

  const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);
  const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);
  const selectedOutletWaiterId = CommonStore.useState(s => s.selectedOutletWaiterId);

  const overrideItemPriceSkuDict = CommonStore.useState(s => s.overrideItemPriceSkuDict);
  const amountOffItemSkuDict = CommonStore.useState(s => s.amountOffItemSkuDict);

  const overrideCategoryPriceNameDict = CommonStore.useState(s => s.overrideCategoryPriceNameDict);
  const amountOffCategoryNameDict = CommonStore.useState(s => s.amountOffCategoryNameDict);

  //////////////////////////////////////////////////////////////////////////////////////

  const [newSelectedOutletItemAddOn, setNewSelectedOutletItemAddOn] = useState({});
  const [newSelectedOutletItemAddOnDetails, setNewSelectedOutletItemAddOnDetails] = useState({});

  const isOrdering = CommonStore.useState((s) => s.isOrdering);

  //////////////////////////////////////////////////////////////////////////////////////

  const isPlacingReservation = CommonStore.useState((s) => s.isPlacingReservation);

  //////////////////////////////////////////////////////////////////////////////////////

  // 2024-12-13 - add on group's min and max choices support

  const selectedAddOnIdForChoiceQtyDict = CommonStore.useState(s => s.selectedAddOnIdForChoiceQtyDict);

  const [addOnMinMaxMessageResultList, setAddOnMinMaxMessageResultList] = useState([]);
  const [addOnMinMaxMessageResultDict, setAddOnMinMaxMessageResultDict] = useState({});
  const [isMaxAddonsTest, setisMaxAddonsTest] = useState(false)
  const insets = useSafeAreaInsets();
  const upsellingCampaignsAfterCart = DataStore.useState(s => s.upsellingCampaignsAfterCart);

  //////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  const [sortedVariantAddOnList, setSortedVariantAddOnList] = useState([]);

  useEffect(() => {
    let sortedVariantAddOnListTemp = [];

    if (selectedOutletItem && selectedOutletItem.uniqueId && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
      sortedVariantAddOnListTemp = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter((item) => {
        const hidden = item.isHideAddOn === true || item.isHideVariant === true || item.hqr === true;
        if (hidden) return false;

        const ots = item.ots || [];
        return ots.length === 0 || ots.includes('DEFAULT') || ots.includes(orderType); // <-- the main filter
      });

      sortedVariantAddOnListTemp = sortedVariantAddOnListTemp.sort((a, b) => {
        return (
          ((a.orderIndex !== undefined)
            ? a.orderIndex
            : sortedVariantAddOnListTemp.length) -
          ((b.orderIndex !== undefined)
            ? b.orderIndex
            : sortedVariantAddOnListTemp.length)
        );
      });
    }

    setSortedVariantAddOnList(sortedVariantAddOnListTemp);
  }, [selectedOutletItem, outletsItemAddOnDict, orderType]);

  useFocusEffect(
    useCallback(() => {
      CommonStore.update(s => {
        s.currPageIframe = 'MenuItemDetails';
      });
    }, [])
  );

  useEffect(() => {
    var variablePriceTemp = '0.00';

    if (selectedOutletItem &&
      // menuItem.uniqueId &&
      selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) {
      if (parseFloat(variablePrice) <= 0) {
        variablePriceTemp = selectedOutletItem.price.toFixed(2);
      }
    }

    if (onUpdatingCartItem &&
      onUpdatingCartItem.priceVariable !== undefined) {
      if (parseFloat(onUpdatingCartItem.priceVariable) >= 0) {
        variablePriceTemp = onUpdatingCartItem.priceVariable.toFixed(2);
      }
    }

    setVariablePrice(variablePriceTemp);
  }, [selectedOutletItem, onUpdatingCartItem]);

  useEffect(() => {
    setNewSelectedOutletItemAddOnDetails({});

    CommonStore.update(s => {
      s.selectedOutletItemAddOn = {};
      s.selectedOutletItemAddOnChoice = {};
    });
  }, [
    selectedOutletItem,
    onUpdatingCartItem,
  ]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (selectedOutletItem && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
        var newSelectedOutletItemAddOnDetailsTemp = { ...newSelectedOutletItemAddOnDetails };

        var addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
          (item) => item.minSelect === undefined && item.maxSelect === undefined
        );

        if (addOnList.length > 0) {
          for (var i = 0; i < addOnList.length; i++) {
            var addOn = addOnList[i];
            if (addOn && outletsItemAddOnChoiceDict[addOn.uniqueId]) {
              var addOnChoiceList = outletsItemAddOnChoiceDict[addOn.uniqueId];
              for (var j = 0; j < addOnChoiceList.length; j++) {
                var addOnChoice = addOnChoiceList[j];
                if (!newSelectedOutletItemAddOnDetailsTemp[addOnChoice.uniqueId]) {
                  newSelectedOutletItemAddOnDetailsTemp[addOnChoice.uniqueId] = {
                    quantity: 0,
                    price: addOnChoice.price,
                    outletItemAddOnChoiceId: addOnChoice.uniqueId,
                    outletItemAddOnId: addOn.uniqueId,
                    choiceName: addOnChoice.name,
                    addOnName: addOn.name,
                    addOnId: addOn.uniqueId,
                    minSelect: addOnChoice.minSelect,
                    maxSelect: addOnChoice.maxSelect,
                    oi: addOn.orderIndex !== undefined ? addOn.orderIndex : 0,
                    pal: addOn.pal !== undefined ? addOn.pal : null,
                    addOnMin: addOn.min ? addOn.min : 0,
                    addOnMax: addOn.max ? addOn.max : 0,
                    ...(addOn.skipSc) && { skipSc: addOn.skipSc },
                    ...(addOn.hqr) && { hqr: addOn.hqr },
                  };
                }
              }
            }
          }
        }

        // Auto Select Choices
        let selectedOutletItemAddOnTemp = { ...selectedOutletItemAddOn };
        let selectedOutletItemAddOnChoiceTemp = { ...selectedOutletItemAddOnChoice };

        const addons = outletsItemAddOnDict[selectedOutletItem.uniqueId];
        let hasAutoSelectItems = false;

        addons.forEach(addon => {
          const isVariant = addon.minSelect !== undefined && addon.maxSelect !== undefined;

          if (addon.isGroupAutoSelect && outletsItemAddOnChoiceDict[addon.uniqueId]) {
            const choices = outletsItemAddOnChoiceDict[addon.uniqueId];

            if (choices) {
              const autoSelectChoices = choices.filter(choice => choice.isAutoSelect);

              if (autoSelectChoices && autoSelectChoices.length > 0) {
                hasAutoSelectItems = true;

                // Variant Auto Select
                if (isVariant) {
                  if (!selectedOutletItemAddOnTemp[addon.uniqueId]) {
                    selectedOutletItemAddOnTemp[addon.uniqueId] = {};
                  }

                  autoSelectChoices.forEach(choice => {
                    selectedOutletItemAddOnTemp[addon.uniqueId] = {
                      ...selectedOutletItemAddOnTemp[addon.uniqueId],
                      [choice.uniqueId]: true
                    };
                    selectedOutletItemAddOnChoiceTemp[choice.uniqueId] = true;
                  });
                }
                // Add On Auto Select
                else {
                  autoSelectChoices.forEach(choice => {
                    newSelectedOutletItemAddOnDetailsTemp[choice.uniqueId] = {
                      quantity: 1,
                      price: choice.price,
                      outletItemAddOnChoiceId: choice.uniqueId,
                      outletItemAddOnId: addon.uniqueId,
                      choiceName: choice.name,
                      addOnName: addon.name,
                      addOnId: addon.uniqueId,
                      minSelect: choice.minSelect,
                      maxSelect: choice.maxSelect,
                      oi: addon.orderIndex !== undefined ? addon.orderIndex : 0,
                      pal: addon.pal !== undefined ? addon.pal : null,
                      addOnMin: addon.min ? addon.min : 0,
                      addOnMax: addon.max ? addon.max : 0,
                      ...(addon.skipSc) && { skipSc: addon.skipSc },
                      ...(addon.hqr) && { hqr: addon.hqr },
                    };
                  });
                }
              }
            }
          }
        });

        CommonStore.update(s => {
          if (hasAutoSelectItems) {
            s.selectedOutletItemAddOn = selectedOutletItemAddOnTemp;
            s.selectedOutletItemAddOnChoice = selectedOutletItemAddOnChoiceTemp;
          }
          setNewSelectedOutletItemAddOnDetails(newSelectedOutletItemAddOnDetailsTemp);
        });

        if (hasAutoSelectItems) {
          setTimeout(() => {
            let addOnPriceTemp = 0;

            Object.entries(selectedOutletItemAddOnTemp).forEach(([addOnId, addOnChoices]) => {
              Object.entries(addOnChoices).forEach(([choiceId, isSelected]) => {
                if (isSelected) {
                  const actualChoiceList = outletsItemAddOnChoiceDict[addOnId];
                  if (actualChoiceList) {
                    for (let i = 0; i < actualChoiceList.length; i++) {
                      if (actualChoiceList[i].uniqueId === choiceId) {
                        addOnPriceTemp += actualChoiceList[i].price;
                      }
                    }
                  }
                }
              });
            });

            Object.values(newSelectedOutletItemAddOnDetailsTemp).forEach(addon => {
              if (addon.quantity > 0 && !addon.hqr
              ) {
                addOnPriceTemp += addon.quantity * addon.price;
              }
            });

            if (addOnPriceTemp > 0) {
              setAddOnPrice(addOnPriceTemp);
            }
          }, 0);
        }
      }
    });
  }, [selectedOutletItem, outletsItemAddOnDict, outletsItemAddOnChoiceDict]);

  useEffect(() => {
    if (selectedOutletItem) {
      const category = selectedOutletItemCategoriesDict[selectedOutletItem.categoryId];

      if (category) {
        var matchedLoyaltyStampId = null;

        var qualifiedLoyaltyStampBuyTemp = {};

        if (loyaltyStampBuyItemSkuDict[selectedOutletItem.sku]) {
          // check if got item based or not first

          matchedLoyaltyStampId = loyaltyStampBuyItemSkuDict[selectedOutletItem.sku].loyaltyStampId;

          qualifiedLoyaltyStampBuyTemp = loyaltyStampBuyItemSkuDict[selectedOutletItem.sku];
        }
        else if (loyaltyStampBuyCategoryNameDict[category.name]) {
          // then only check category based one

          matchedLoyaltyStampId = loyaltyStampBuyCategoryNameDict[category.name].loyaltyStampId;

          qualifiedLoyaltyStampBuyTemp = loyaltyStampBuyCategoryNameDict[category.name];
        }

        if (matchedLoyaltyStampId) {
          var qualifiedLoyaltyStampsTemp = [];

          for (var i = 0; i < selectedOutletLoyaltyStamps.length; i++) {
            if (selectedOutletLoyaltyStamps[i].uniqueId === matchedLoyaltyStampId) {
              qualifiedLoyaltyStampsTemp.push(selectedOutletLoyaltyStamps[i]);
              break;
            }
          }

          setQualifiedLoyaltyStamps(qualifiedLoyaltyStampsTemp);
          setQualifiedLoyaltyStampBuy(qualifiedLoyaltyStampBuyTemp);
        }
      }
    }
  }, [
    selectedOutletLoyaltyStamps,
    selectedOutletItem,
    selectedOutletItemCategoriesDict,
    loyaltyStampBuyItemSkuDict,
    loyaltyStampBuyCategoryNameDict,
  ]);

  // 2022-10-12 - Hide first
  // useEffect(() => {
  //   if (isPlacingReservation) {

  //   }
  //   else {
  //     if (orderType === ORDER_TYPE.DINEIN && userCart.uniqueId === undefined) {
  //       linkTo && linkTo(`${prefix}/scan`);
  //     }
  //   }
  // }, [userCart, orderType, isPlacingReservation]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          if (isPlacingReservation) {
          } else {
            // if (
            //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
            //   // 2022-10-08 - Try to disable this
            //   // &&
            //   // commonStoreData.userCart.uniqueId === undefined
            // ) {
            //   // logout the user

            //   linkTo && linkTo(`${prefix}/scan`);
            // }
          }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  useEffect(() => {
    if (onUpdatingCartItem) {
      setQuantity(onUpdatingCartItem.quantity);

      return () => {
        CommonStore.update(s => {
          s.onUpdatingCartItem = null;
        });
      }
    }
  }, [onUpdatingCartItem]);

  useEffect(() => {
    if (selectedOutletItem && selectedOutletItem.price !== undefined) {
      var extraPrice = 0;
      if (
        orderType === ORDER_TYPE.DELIVERY &&
        selectedOutlet &&
        selectedOutlet.deliveryPrice
      ) {
        extraPrice = selectedOutlet.deliveryPrice;
      } else if (
        orderType === ORDER_TYPE.PICKUP &&
        selectedOutlet &&
        selectedOutlet.pickUpPrice
      ) {
        extraPrice = selectedOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = selectedOutletItem.deliveryCharges || 0;

        if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP) {
        extraPrice = selectedOutletItem.pickUpCharges || 0;

        if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = selectedOutletItem.price * extraPrice / 100;
        }

        if (!selectedOutletItem.pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      var overrideCategoryPrice = undefined;
      if (selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
        overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
      }

      if (overrideItemPriceSkuDict[selectedOutletItem.sku] && overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice !== undefined) {
        setTotalPrice(overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice);
      }
      else if (overrideCategoryPrice !== undefined) {
        setTotalPrice(overrideCategoryPrice);
      }
      else {
        setTotalPrice(extraPrice + selectedOutletItem.price);
      }
    }
  }, [selectedOutletItem, overrideItemPriceSkuDict, overrideCategoryPriceNameDict]);

  const [addOnMinMaxMessage, setAddOnMinMaxMessage] = useState('');

  useEffect(() => {
    const addOnList = Object.entries(selectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));

    var addOnPriceTemp = 0;

    for (var i = 0; i < addOnList.length; i++) {
      var isValid = false;

      if (outletsItemAddOnDict[selectedOutletItem.uniqueId] && outletsItemAddOnDict[selectedOutletItem.uniqueId].length > 0) {
        for (var j = 0; j < outletsItemAddOnDict[selectedOutletItem.uniqueId].length; j++) {
          if (outletsItemAddOnDict[selectedOutletItem.uniqueId][j].outletItemId === selectedOutletItem.uniqueId) {
            isValid = true;
          }
        }
      }

      if (isValid && addOnList[i].value) {
        const addOnChoiceList = Object.entries(addOnList[i].value).map(
          ([key, value]) => ({ key: key, value: value }),
        );
        for (var j = 0; j < addOnChoiceList.length; j++) {
          // console.log(addOnChoiceList[j].value);
          // console.log(outletsItemAddOnChoiceDict[addOnList[i].key]);
          // console.log(outletsItemAddOnChoiceDict[addOnList[i].key][addOnChoiceList[j].key]);

          if (addOnChoiceList[j].value) {
            const actualAddOnChoiceList = outletsItemAddOnChoiceDict[addOnList[i].key];

            if (actualAddOnChoiceList && actualAddOnChoiceList.length > 0) {
              for (var k = 0; k < actualAddOnChoiceList.length; k++) {
                if (addOnChoiceList[j].key === actualAddOnChoiceList[k].uniqueId) {
                  // add this addon price

                  addOnPriceTemp += actualAddOnChoiceList[k].price;
                }
              }
            }
          }
        }
      }
    }

    //////////////////////////////////////////

    // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));

    // for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
    //   if (newSelectedOutletItemAddOnList[i].value) {
    //     const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

    //     if (addOnTemp) {
    //       addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;
    //     }
    //   }
    // }

    var addOnMinMaxMessageTemp = '';
    let selectedAddOnIdForChoiceQtyDictTemp = {};

    // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
    const newSelectedOutletItemAddOnList = Object.entries(
      newSelectedOutletItemAddOnDetails,
    ).map(([key, value]) => ({ key: key, value: value }));

    for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
      if (newSelectedOutletItemAddOnList[i].value) {
        const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

        if (addOnTemp && !addOnTemp.hqr) {
          addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;

          if (selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId]) {
            selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId].countedQty += addOnTemp.quantity;
          }
          else {
            selectedAddOnIdForChoiceQtyDictTemp[addOnTemp.outletItemAddOnId] = {
              ...addOnTemp,

              countedQty: addOnTemp.quantity,
            };
          }

          ///////////////////////////////

          // checks addOn minSelect and maxSelect

          if (addOnTemp.quantity < addOnTemp.minSelect || addOnTemp.quantity > addOnTemp.maxSelect) {
            addOnMinMaxMessageTemp += `${addOnTemp.choiceName}'s quantity must be within ${addOnTemp.minSelect} ~ ${addOnTemp.maxSelect}\n`;
          }

          ///////////////////////////////
        }
      }
    }

    //////////////////////////////////////////

    const selectedAddOnIdForChoiceQtyDictTempList = Object.entries(selectedAddOnIdForChoiceQtyDictTemp).map(([key, value]) => {
      return {
        key: key,
        ...value,
      };
    });
    if (sortedVariantAddOnList.length > 0) {
      for (let vaIndex = 0; vaIndex < sortedVariantAddOnList.length; vaIndex++) {
        const vaGroup = sortedVariantAddOnList[vaIndex];

        if (vaGroup.min > 0 || vaGroup.max > 0) {
          let vaChecked = false;

          for (let i = 0; i < selectedAddOnIdForChoiceQtyDictTempList.length; i++) {
            const addOnIdForChoice = selectedAddOnIdForChoiceQtyDictTempList[i];

            if (addOnIdForChoice.addOnId === vaGroup.uniqueId) {
              if (addOnIdForChoice.addOnMin > 0 || addOnIdForChoice.addOnMax > 0) {
                if (addOnIdForChoice.countedQty < addOnIdForChoice.addOnMin || addOnIdForChoice.countedQty > addOnIdForChoice.addOnMax) {
                  addOnMinMaxMessageTemp += `${addOnIdForChoice.addOnName}'s choices quantities must within ${addOnIdForChoice.addOnMin} ~ ${addOnIdForChoice.addOnMax}\n`;
                }
              }

              vaChecked = true;
              break;
            }
            else {
              // if (vaGroup.min > 0) {
              //     addOnMinMaxMessageTemp += `${vaGroup.name}'s choices quantities must within ${vaGroup.min} ~ ${vaGroup.max}\n`;

              //     vaChecked = true;
              //     break;
              // }
            }
          }

          if (!vaChecked) {
            if (vaGroup.min > 0) {
              addOnMinMaxMessageTemp += `${vaGroup.name}'s choices quantities must within ${vaGroup.min} ~ ${vaGroup.max}\n`;

              vaChecked = true;
              break;
            }
          }
        }
      }
    }

    //////////////////////////////////////////

    setAddOnMinMaxMessage(addOnMinMaxMessageTemp);

    //////////////////////////////////////////

    setAddOnPrice(addOnPriceTemp);

    CommonStore.update(s => {
      s.selectedAddOnIdForChoiceQtyDict = selectedAddOnIdForChoiceQtyDictTemp;
    });
  }, [
    selectedOutletItemAddOn,
    selectedOutletItem,
    outletsItemAddOnChoiceDict,
    outletsItemAddOnDict,

    newSelectedOutletItemAddOn,
    newSelectedOutletItemAddOnDetails,

    sortedVariantAddOnList,
  ]);

  // auto init the selected addon dict with 0, then later can check

  // useEffect(() => {
  //   var newSelectedOutletItemAddOnDetailsAppend = {};

  //   if (
  //     selectedOutletItem &&
  //     outletsItemAddOnDict[selectedOutletItem.uniqueId]
  //   ) {
  //     var addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(item => item.minSelect === undefined && item.maxSelect === undefined);

  //     if (addOnList.length > 0) {
  //       for (var i = 0; i < addOnList.length; i++) {
  //         var addOn = addOnList[i];

  //         if (
  //           addOn &&
  //           outletsItemAddOnChoiceDict[addOn.uniqueId]
  //         ) {
  //           var addOnChoiceList = outletsItemAddOnChoiceDict[addOn.uniqueId];

  //           for (var j = 0; j < addOnChoiceList.length; j++) {
  //             var addOnChoice = addOnChoiceList[j];

  //             if (!newSelectedOutletItemAddOnDetails[addOnChoice.uniqueId]) {
  //               // means not exist

  //               newSelectedOutletItemAddOnDetailsAppend[addOnChoice.uniqueId] = {
  //                 quantity: 0,
  //                 price: addOnChoice.price,

  //                 outletItemAddOnChoiceId: addOnChoice.uniqueId,
  //                 outletItemAddOnId: addOn.uniqueId,

  //                 choiceName: addOnChoice.name,
  //                 addOnName: addOn.name,
  //                 addOnId: addOn.uniqueId,

  //                 minSelect: addOnChoice.minSelect,
  //                 maxSelect: addOnChoice.maxSelect,

  //                 oi: addOn.orderIndex !== undefined ? addOn.orderIndex : 0,

  //                 pal: addOn.pal !== undefined ? addOn.pal : null,

  //                 addOnMin: addOn.min ? addOn.min : 0,
  //                 addOnMax: addOn.max ? addOn.max : 0,

  //                 ...(addOn.skipSc) && {
  //                   skipSc: addOn.skipSc,
  //                 },

  //               ...(addOn.hqr) && {
  //                 hqr: addOn.hqr,
  //               },

  //               ...(addOn.ots) && {
  //                 ots: addOn.ots,
  //               },
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }
  // }

  //   setNewSelectedOutletItemAddOnDetails({
  //     ...newSelectedOutletItemAddOnDetails,
  //     ...newSelectedOutletItemAddOnDetailsAppend,
  //   });
  // }, [
  //   selectedOutletItem,
  //   outletsItemAddOnDict,
  //   outletsItemAddOnChoiceDict,
  // ]);

  useEffect(() => {
    // options = cartItem ? cartItem.options : [];
    // quantity2 = cartItem ? cartItem.quantity : quantity;
    // total = cartItem ? setState({ total: cartItem.price }) : total;
    // refreshAction = refresh;

    // ApiClient.GET(API.getItemAddOnChoice + menuItem.id).then(
    //   (result) => {
    //     setState({ menuItemDetails: result });
    //   }
    // );

    // setInterval(() => {
    //   cartCount();
    // }, 5000);

    cartCount();
  }, []);

  /////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   if (selectedOutletItem && selectedOutletItem.uniqueId) {
  //     for (var i = 0; i < outletsItemAddOnDict[selectedOutletItem.uniqueId].length; i++) {
  //       const addOn = outletsItemAddOnDict[selectedOutletItem.uniqueId][i];

  //       for (var j = 0; j < outletsItemAddOnChoiceDict[addOn.uniqueId]; j++) {
  //         if ()
  //       }
  //     }
  //   }
  // }, [selectedOutletItem, outletsItemAddOnDict])

  const isLoading = CommonStore.useState(s => s.isLoading);

  const [addOnVerified, setAddOnVerified] = useState(false);
  const [modalQuantity, setModalQuantity] = useState(1)
  const [addOnVerifiedResultList, setAddOnVerifiedResultList] = useState([]);

  const [addOnVerifiedResultDict, setAddOnVerifiedResultDict] = useState({});
  const addOnScrollViewRef = useRef(null);

  useEffect(() => {
    if (!isLoading) {
      if (selectedOutletItem && selectedOutletItem.uniqueId) {
        const addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId]
          ? outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
            (item) => {
              // !(item.isHideAddOn === true || item.isHideVariant === true)

              const hidden = item.isHideAddOn === true || item.isHideVariant === true
              // || item.hqr === true;
              if (hidden) return false;

              const ots = item.ots || [];
              return ots.length === 0 || ots.includes('DEFAULT') || (orderType === ORDER_TYPE_SUB.OTHER_DELIVERY && ots.includes(ORDER_TYPE.DELIVERY)) || (orderType === ORDER_TYPE_SUB.NORMAL && ots.includes(orderType)); // <-- the main filter
            }
          )
          : [];

        if (addOnList && addOnList.length > 0) {
          // got addons

          var resultList = [];

          for (var i = 0; i < addOnList.length; i++) {
            if (addOnList[i].minSelect !== undefined && addOnList[i].maxSelect !== undefined) {
              var result = false;

              const addOnId = addOnList[i].uniqueId;
              const minSelect = addOnList[i].minSelect;
              const maxSelect = addOnList[i].maxSelect;

              if (minSelect === 0) {
                result = true;
              } else if (selectedOutletItemAddOn[addOnId]) {
                const selectedOutletItemAddOnValueList = Object.entries(selectedOutletItemAddOn[addOnId]).map(([key, value]) => (value));

                const selectedCount = selectedOutletItemAddOnValueList.filter(value => value === true).length;

                if (selectedCount >= minSelect && selectedCount <= maxSelect) {
                  result = true;
                }
              }

              resultList.push(result);
            }
            else {
              resultList.push(true);
            }
          }

          setAddOnVerified(resultList.filter(result => result === false).length === 0);
          // setAddOnVerifiedResultList(resultList.filter(result => result === false));
        }
        else {
          setAddOnVerified(true);
          // setAddOnVerifiedResultList([]);
        }
      }
    }
  }, [isLoading, selectedOutletItem, outletsItemAddOnDict, outletsItemAddOnChoiceDict, selectedOutletItemAddOn]);

  /////////////////////////////////////////////////////////////////

  const expandChoiceFunc = (param) => {
    if (expandChoice == false) {
      return setState({ expandChoice: true }), (param.expandChoice = true);
    } else {
      return setState({ expandChoice: false }), (param.expandChoice = false);
    }
  };

  const cartCount = () => {
    if (Cart.getCartItem().length > 0) {
      setState({ cartIcon: true });
    } else {
      setState({ cartIcon: false });
    }
  };

  const changeClick = () => {
    if (clicked == 1) {
      setState({ clicked: 0 });
    } else {
      setState({ clicked: 1, clicked1: 0, clicked2: 0, size: 'small' });
    }
  };

  const changeClick1 = () => {
    if (clicked1 == 1) {
      setState({ clicked1: 0 });
    } else {
      setState({ clicked1: 1, clicked: 0, clicked2: 0, size: 'medium' });
    }
  };
  const changeClick2 = () => {
    if (clicked2 == 1) {
      setState({ clicked2: 0 });
    } else {
      setState({ clicked2: 1, clicked: 0, clicked1: 0, size: 'big' });
    }
  };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() });
  };

  // function here
  const addToCart = () => {
    // Cart.setTax(5);
    // if (Cart.getOutletId() != outletData.id) {
    //   Cart.setOutletId(outletData.id);
    //   Cart.clearCart();
    // }
    // options.forEach((element, index) => {
    //   if (element.quantity == 0) options.splice(index, 1);
    // });
    // var data = {
    //   itemId: menuItem.id,
    //   quantity: quantity,
    //   remarks: remark,
    //   options: options,
    //   image: menuItem.image,
    //   name: menuItem.name,
    //   price: parseFloat(totalState),
    //   fireOrder: optional,
    //   menuItem: menuItem,
    //   size: size
    // };
    // Cart.setCartItem(data);
    // Cart.setOutletId(outletData.id);
    // setState({ refresh: true });
    // refreshAction();
    // Cart.getRefreshCartPage();
  }

  const showPrice = () => {
    // if (price == false) {
    //   setState({ price: true });
    // } else {
    //   setState({ price: false });
    // }
  }

  const addToCart1 = () => {
    // Cart.setTax(5);
    // if (Cart.getOutletId() != outletData.id) {
    //   Cart.setOutletId(outletData.id);
    //   Cart.clearCart();
    // }
    // options.forEach((element, index) => {
    //   if (element.quantity == 0) options.splice(index, 1);
    // });
    // var data = {
    //   itemId: menuItem.id,
    //   quantity: quantity,
    //   remark: remark,
    //   options: options,
    //   image: menuItem.image,
    //   name: menuItem.name,
    //   price: parseFloat(totalState),
    //   fireOrder: optional1,
    //   menuItem: menuItem,
    // };
    // Cart.setCartItem(data);
    // Cart.setOutletId(outletData.id);
    // setState({ refresh: true });
    // refreshAction();
    // Cart.getRefreshCartPage();
  }

  const containsObject = (id) => {
    // var i;
    // for (i = 0; i < options.length; i++) {
    //   if (options[i].choice === id) {
    //     return true;
    //   }
    // }
    // return false;
  }

  const addLeastItem = (name, choiceId, price, cat) => {
    // var i;
    // var total = 0;
    // var prevChoice = 0;
    // for (i = 0; i < options.length; i++) {
    //   if (options[i].least == true && options[i].cat == cat) {
    //     prevChoice = options[i].choice
    //     total =
    //       parseFloat(totalState) -
    //       options[i].price * quantity1;
    //     options.splice(i, 1);
    //   }
    // }
    // if (prevChoice != choiceId) {
    //   var choice = {
    //     choice: choiceId,
    //     quantity1: 1,
    //     itemName: name,
    //     least: true,
    //     cat: cat,
    //     price: price
    //   };
    //   // setState({
    //   //   total:
    //   //     (total != 0 ? total : parseFloat(total)) +
    //   //     parseFloat(price) * quantity1,
    //   // });
    //   setTotalState((total != 0 ? total : parseFloat(totalState)) +
    //     parseFloat(price) * quantity1);
    //   options.push(choice);
    // } else {
    //   // setState({ total: total })
    //   setTotalState(total)
    // }
    // setState({ refresh: true });
  }

  const addOption = (name, quantity, choiceId, price) => {
    // if (quantity > 0) {
    //   if (!containsObject(choiceId, options)) {
    //     var choice = {
    //       choice: choiceId,
    //       quantity: 1,
    //       itemName: "Add " + name,
    //       least: false,
    //       price: price,
    //     };
    //     // setState({
    //     //   total: parseFloat(totalState) + parseFloat(price),
    //     // });
    //     setState(parseFloat(totalState) + parseFloat(price));
    //     options.push(choice);
    //   } else {
    //     var i;
    //     for (i = 0; i < options.length; i++) {
    //       if (options[i].choice === choiceId) {
    //         options[i].quantity = options[i].quantity + 1;
    //         // setState({
    //         //   total: parseFloat(total) + parseFloat(price),
    //         // });
    //         setState(parseFloat(totalState) + parseFloat(price));
    //       }
    //     }
    //   }
    // } else {
    //   var i;
    //   for (i = 0; i < options.length; i++) {
    //     if (options[i].choice === choiceId) {
    //       if (options[i].quantity > 0) {
    //         options[i].quantity = options[i].quantity + quantity;
    //         if (options[i] - 1 == 0) {
    //           options.splice(i, 1);
    //         }
    //         // setState({
    //         //   total: parseFloat(total) - parseFloat(price),
    //         // });
    //         setState(parseFloat(totalState) + parseFloat(price));
    //       } else {
    //         options.splice(i, 1);
    //       }
    //     }
    //   }
    // }
  }

  const getQuantity = (itemId) => {
    // var i;
    // for (i = 0; i < cartItem.length; i++) {
    //   if (cartItem[i].itemId === itemId) {
    //     quantity = options[i].quantity;
    //   }
    // }
    // return quantity;
  }

  const getOptionQuantity = (choiceId) => {
    // var quantity = 0;
    // var i;
    // for (i = 0; i < options.length; i++) {
    //   if (options[i].choice === choiceId) {
    //     quantity = options[i].quantity;
    //   }
    // }
    // return quantity;
  }

  const quantityFunc = () => {
    // if (quantity == null) {
    //   setState({ quantity: 1 });
    //   return quantity
    // } else if (cartItem != null) {
    //   return quantity2;
    // } else {
    //   return quantity;
    // }
  }
  // onChangeQty(e, id) {
  //   const cartItem = cartItem;
  //   console.log(cartItem);
  //   const item = cartItem.find((obj) => obj.itemId === id);
  //   item.quantity = e;
  //   setState({
  //     cartItem,
  //   });
  // }

  const goToCart = () => {
    if (Cart.getCartItem().length > 0) {
      props.navigation.navigate('Cart', {
        test: test,
        // outletData: outletData,
        // menuItem: menuItem,
        clicked: clicked,
        clicked1: clicked1,
        clicked2: clicked2,
      })
    } else {
      window.confirm("Info", "No items in your cart at the moment", [
        { text: "OK", onPress: () => { } }
      ],
        { cancelable: false })
    }
  }

  const lessqty = (id) => {
    // if (cartItem != null) {
    //   const cartItem = cartItem;
    //   cartItem.quantity = cartItem.quantity - 1;
    //   save();
    // } else {
    //   return setState({ quantity: quantity - 1 });
    // }
  }

  const addqty = (id) => {
    // if (cartItem != null) {
    //   const cartItem = cartItem;
    //   cartItem.quantity = cartItem.quantity + 1;
    //   save();
    // } else {
    //   return setState({ quantity: quantity + 1 });
    // }
  }

  const totals = () => {
    // if (cartItem != null) {
    //   return totalState;
    // } else {
    //   return totalState;
    // }
  }

  const save = () => {
    // if (cartItem != null) {
    //   return goToCart();
    // } else {
    //   return setState({ visible: true });
    // }
  }

  const updateUserCart = async (newCartItems) => {
    // const body = {
    //   userId: firebaseUid,
    //   outletId: selectedOutlet.uniqueId,
    //   tableId: selectedOutletTableId,
    //   tablePax: selectedOutletTablePax,
    //   cartItems: newCartItems,

    //   waiterId: selectedOutletWaiterId,
    // };

    // ApiClient.POST(API.updateUserCart, body).then((result) => {
    //   if (result && result.status === 'success') {
    //     console.log('ok');
    //   }
    // });
  };

  const renderVariants = (dataItem, item, index) => {
    const item2 = dataItem.item;

    return (
      <>
        {//////////////////////////////////////////////////////////////////

          // // 2022-07-19 - Added variant Image for item - Eric Cheng Num Keet
        }
        <View>
          {
            (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
              ?
              <>
                {item2.image ? (
                  <AsyncImage
                    source={{ uri: item2.image }}
                    style={{
                      resizeMode: 'contain',
                      width: 50,
                      height: 50,
                      borderRadius: 10,
                      marginBottom: 15,
                      alignSelf: 'center',
                    }}
                    hideLoading={true}
                  />) : (
                  <View
                    style={{
                      width: 50,
                      height: 50,
                      backgroundColor: Colors.secondaryColor,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 15,
                      alignSelf: 'center',
                    }}>
                    <DefaultImage />
                  </View>
                )
                  //////////////////////////////////////////////////////////////////////
                }
              </>
              :
              <></>
          }
          <TouchableOpacity style={{
            //width: "50%",
            height: "auto",
            // backgroundColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
            // borderColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? 'transparent' : Colors.descriptionColor,
            // borderColor: Colors.descriptionColor,
            // marginHorizontal: 8,
            // paddingHorizontal: 12,
            marginVertical: 10,
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
            onPress={() => {
              /////////////////////////////////
              // minSelect/maxSelect calculations

              var selectedOutletItemAddOnChoiceRemoved = {};

              if (item.minSelect === 1 && item.maxSelect === 1) {
                // means can only choose 1 addon choice, the others need removed

                for (var i = 0; i < outletsItemAddOnChoiceDict[item.uniqueId].length; i++) {
                  selectedOutletItemAddOnChoiceRemoved[outletsItemAddOnChoiceDict[item.uniqueId][i].uniqueId] = false;
                }
              }

              /////////////////////////////////

              var chooseValue = false;
              if (selectedOutletItemAddOnChoice[item2.uniqueId] === undefined ||
                selectedOutletItemAddOnChoice[item2.uniqueId] === false) {
                chooseValue = true;
              }
              else {
                chooseValue = false;
              }

              //////////////////////////////////////////////////////////////////

              // 2023-01-09 - Prevent user from select the choice num exceed the max choice num

              var maxChoices = item.maxSelect;
              var totalChoiceNum = 0;
              var choseChoiceIdList = [];

              if (chooseValue && selectedOutletItemAddOn[item.uniqueId]) {
                for (
                  var i = 0;
                  i <
                  outletsItemAddOnChoiceDict[
                    item.uniqueId
                  ].length;
                  i++
                ) {
                  var currChoiceId = outletsItemAddOnChoiceDict[
                    item.uniqueId
                  ][i].uniqueId;

                  if (selectedOutletItemAddOnChoice[currChoiceId]) {
                    // means selected

                    totalChoiceNum++;

                    choseChoiceIdList.push(currChoiceId);
                  }
                }
              }

              if (chooseValue && totalChoiceNum >= maxChoices && choseChoiceIdList.length > 0) {
                // try to remove one of the previous choice

                selectedOutletItemAddOnChoiceRemoved[
                  choseChoiceIdList[0]
                ] = false;

                // chooseValue = false;
              }

              //////////////////////////////////////////////////////////////////

              CommonStore.update(s => {
                s.selectedOutletItemAddOnChoice = {
                  ...selectedOutletItemAddOnChoice,

                  ...selectedOutletItemAddOnChoiceRemoved,

                  [item2.uniqueId]: chooseValue,
                };

                if (selectedOutletItemAddOn[item.uniqueId] === undefined) {
                  s.selectedOutletItemAddOn = {
                    ...selectedOutletItemAddOn,
                    // [item.uniqueId]: new Set(),
                    [item.uniqueId]: {},
                  };
                }

                s.selectedOutletItemAddOn = {
                  ...selectedOutletItemAddOn,
                  [item.uniqueId]: {
                    ...(selectedOutletItemAddOn[item.uniqueId]),

                    ...selectedOutletItemAddOnChoiceRemoved,

                    [item2.uniqueId]: chooseValue,
                  },
                };

                s.selectedOutletItemAddOnOi = {
                  ...selectedOutletItemAddOnOi,
                  [item.uniqueId]: item.orderIndex !== undefined ? item.orderIndex : 0,
                };

                console.log({
                  ...selectedOutletItemAddOn,
                  [item.uniqueId]: {
                    ...(selectedOutletItemAddOn[item.uniqueId]),

                    ...selectedOutletItemAddOnChoiceRemoved,

                    [item2.uniqueId]: chooseValue,
                  },
                });
              });
            }}>
            {/* <Text
              testID={`${index}-variantName-${dataItem.index}`}
              style={{
                alignSelf: "center",
                color: selectedOutletItemAddOnChoice[item2.uniqueId] ? Colors.whiteColor : Colors.descriptionColor,
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
              }}>{`${item2.name} (+RM${item2.price.toFixed(2)})`}</Text> */}
            <View style={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap' }}>
              <Text
                testID={`${index}-variantName-${dataItem.index}`}
                style={{
                  alignSelf: "center",
                  color: Colors.blackColor,
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: 14,
                  maxWidth: '90%',
                }}>{`${item2.name}`}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{
                alignSelf: "center",
                color: Colors.blackColor,
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
                paddingRight: 5,
              }}>{`+ RM ${item2.price.toFixed(2)}`}
              </Text>

              {/* <CheckboxLoadable fallback={<></>}>
                                {({ default: Checkbox }) =>
                                    <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
                                }
                            </CheckboxLoadable> */}

              <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
            </View>
          </TouchableOpacity>
        </View>
      </>
    );
  };

  const renderAddons = (dataItem, item) => {
    const item2 = dataItem.item;

    return (
      <View style={{
        width: 'auto%',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
        marginBottom: 10,
        justifyContent: 'space-between',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '45%',
          // backgroundColor: 'red',
        }}>
          {//////////////////////////////////////////////////////////////////

            // // 2022-07-19 - Added addon Image for item - Eric Cheng Num Keet
          }
          {
            (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
              ?
              <>
                {item2.image ? (
                  <AsyncImage
                    source={{ uri: item2.image }}
                    style={{
                      resizeMode: 'contain',
                      width: 50,
                      height: 50,
                      borderRadius: 10,
                      marginRight: 20,
                    }}
                    hideLoading={true}
                  />) : (
                  <View
                    style={{
                      width: 50,
                      height: 50,
                      backgroundColor: Colors.secondaryColor,
                      borderRadius: 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 20,
                    }}>
                    <DefaultImage />
                  </View>
                )
                  //////////////////////////////////////////////////////////////////////
                }
              </>
              :
              <></>
          }

          <Text style={{
            fontFamily: isMaxAddonsTest && item2.name === "Dessert 2" ? 'NunitoSans-Regular' : 'NunitoSans-Bold',
            fontSize: 14,
            color: isMaxAddonsTest && item2.name === "Dessert 2" ? Colors.tabGrey : Colors.blackColor
            // marginLeft: 15,
          }}>
            {item2.name}
          </Text>
        </View>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          // backgroundColor: 'red',                                
          justifyContent: 'space-between',
          // width: windowWidth * 0.4,
          width: '55%',
        }}>
          <View
            style={{
              flexDirection: "row",
              borderWidth: 1,
              borderRadius: 25,
              borderColor: isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor,
              alignItems: 'center',
              width: '35%',
              justifyContent: 'space-around'
            }}>
            <TouchableOpacity
              onPress={() => {
                if (global.outletName) {
                  logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                  });
                }

                var quantityTemp = 0;

                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                  quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                  quantityTemp = quantityTemp - 1 >= 0 ? quantityTemp - 1 : 0;

                  setNewSelectedOutletItemAddOnDetails({
                    ...newSelectedOutletItemAddOnDetails,
                    [item2.uniqueId]: {
                      ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                      quantity: quantityTemp,
                      price: item2.price,

                      outletItemAddOnChoiceId: item2.uniqueId,
                      outletItemAddOnId: item.uniqueId,

                      choiceName: item2.name,
                      addOnName: item.name,

                      addOnId: item.uniqueId,

                      minSelect: item2.minSelect,
                      maxSelect: item2.maxSelect,

                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                      pal: item.pal !== undefined ? item.pal : null,

                      addOnMin: item.min ? item.min : 0,
                      addOnMax: item.max ? item.max : 0,

                      ...(item.skipSc) && {
                        skipSc: item.skipSc,
                      },

                      ...(item.hqr) && {
                        hqr: item.hqr,
                      },

                      ...(item.ots) && {
                        ots: item.ots,
                      },
                    }
                  });
                }
                else {
                  setNewSelectedOutletItemAddOnDetails({
                    ...newSelectedOutletItemAddOnDetails,
                    [item2.uniqueId]: {
                      quantity: quantityTemp,
                      price: item2.price,

                      outletItemAddOnChoiceId: item2.uniqueId,
                      outletItemAddOnId: item.uniqueId,

                      choiceName: item2.name,
                      addOnName: item.name,

                      addOnId: item.uniqueId,

                      minSelect: item2.minSelect,
                      maxSelect: item2.maxSelect,

                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                      pal: item.pal !== undefined ? item.pal : null,

                      addOnMin: item.min ? item.min : 0,
                      addOnMax: item.max ? item.max : 0,

                      ...(item.skipSc) && {
                        skipSc: item.skipSc,
                      },

                      ...(item.hqr) && {
                        hqr: item.hqr,
                      },

                      ...(item.ots) && {
                        ots: item.ots,
                      },

                    }
                  });
                }
              }}>
              <View testID={`addOnMinus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                <FontAwesome name="minus"
                  color={isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor}
                  size={10}
                />
              </View>
            </TouchableOpacity>
            <View style={[styles.addBtn, { height: 27, }]} >
              <Text
                testID={`addOnQuantity-${dataItem.index}`}
                style={{
                  fontSize: 14,
                  fontFamily: "NunitoSans-Bold",
                  color: isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor,
                }}>
                {newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
                  newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity
                  : 0
                }
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => {
                if (global.outletName) {
                  logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                  });
                }

                let allowToProceed = false;
                if (item.min > 0 || item.max > 0) {
                  const addOnQtyTotal = (selectedAddOnIdForChoiceQtyDict[item.uniqueId] && selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty) ? selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty : 0;

                  if ((addOnQtyTotal + 1) > item.max) {

                  }
                  else {
                    allowToProceed = true;
                  }
                }
                else {
                  allowToProceed = true;
                }

                if (!allowToProceed) {
                  // window.confirm(`Info\n\nThe maximum selection for this add-on is ${item.max}`);

                  Toastify({
                    text: `The maximum selection for this add-on is ${item.max}`,
                    duration: 3000,
                    // destination: "https://github.com/apvarun/toastify-js",
                    newWindow: true,
                    close: false,
                    gravity: "top", // `top` or `bottom`
                    position: "right", // `left`, `center` or `right`
                    stopOnFocus: true, // Prevents dismissing of toast on hover
                    style: {
                      background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                      color: 'white',

                      // marginLeft: '15px !important',
                      // marginRight: '15px !important',
                    },
                    onClick: function () { } // Callback after click
                  }).showToast();

                  return;
                }

                var quantityTemp = 0;

                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                  quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                  quantityTemp = quantityTemp + 1;

                  setNewSelectedOutletItemAddOnDetails({
                    ...newSelectedOutletItemAddOnDetails,
                    [item2.uniqueId]: {
                      ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                      quantity: quantityTemp,
                      price: item2.price,

                      outletItemAddOnChoiceId: item2.uniqueId,
                      outletItemAddOnId: item.uniqueId,

                      choiceName: item2.name,
                      addOnName: item.name,

                      addOnId: item.uniqueId,

                      minSelect: item2.minSelect,
                      maxSelect: item2.maxSelect,

                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                      pal: item.pal !== undefined ? item.pal : null,

                      addOnMin: item.min ? item.min : 0,
                      addOnMax: item.max ? item.max : 0,

                      ...(item.skipSc) && {
                        skipSc: item.skipSc,
                      },

                      ...(item.hqr) && {
                        hqr: item.hqr,
                      },

                      ...(item.ots) && {
                        ots: item.ots,
                      },

                    }
                  });
                }
                else {
                  setNewSelectedOutletItemAddOnDetails({
                    ...newSelectedOutletItemAddOnDetails,
                    [item2.uniqueId]: {
                      quantity: 1,
                      price: item2.price,

                      outletItemAddOnChoiceId: item2.uniqueId,
                      outletItemAddOnId: item.uniqueId,

                      choiceName: item2.name,
                      addOnName: item.name,

                      addOnId: item.uniqueId,

                      minSelect: item2.minSelect,
                      maxSelect: item2.maxSelect,

                      oi: item.orderIndex !== undefined ? item.orderIndex : 0,

                      pal: item.pal !== undefined ? item.pal : null,

                      addOnMin: item.min ? item.min : 0,
                      addOnMax: item.max ? item.max : 0,

                      ...(item.skipSc) && {
                        skipSc: item.skipSc,
                      },

                      ...(item.hqr) && {
                        hqr: item.hqr,
                      },

                      ...(item.ots) && {
                        ots: item.ots,
                      },

                    }
                  });
                }
              }}>
              <View testID={`addOnPlus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                <FontAwesome name="plus"
                  color={isMaxAddonsTest ? Colors.tabGrey : Colors.primaryColor}
                  size={10}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{
            width: '65%',
            flexDirection: 'row',
            // backgroundColor: 'blue',
            justifyContent: 'flex-end',
          }}>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              fontSize: 14,
              marginLeft: 10,
            }}>+ RM {((newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
              (newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity > 0 ? newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity : 1)
              : 1) * item2.price).toFixed(2)
              }</Text>
          </View>
        </View>
      </View>
    );
  };

  // function end

  var overrideCategoryPrice = undefined;
  if (selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
  }

  var extraPrice = 0;
  if (
    orderType === ORDER_TYPE.DELIVERY &&
    selectedOutlet &&
    selectedOutlet.deliveryPrice
  ) {
    extraPrice = selectedOutlet.deliveryPrice;
  } else if (
    orderType === ORDER_TYPE.PICKUP &&
    selectedOutlet &&
    selectedOutlet.pickUpPrice
  ) {
    extraPrice = selectedOutlet.pickUpPrice;
  }

  if (orderType === ORDER_TYPE.DELIVERY) {
    extraPrice = selectedOutletItem.deliveryCharges || 0;

    if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
      extraPrice = selectedOutletItem.price * extraPrice / 100;
    }

    if (!selectedOutletItem.deliveryChargesActive) {
      extraPrice = 0;
    }
  }

  if (orderType === ORDER_TYPE.PICKUP) {
    extraPrice = selectedOutletItem.pickUpCharges || 0;

    if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
      extraPrice = selectedOutletItem.price * extraPrice / 100;
    }

    if (!selectedOutletItem.pickUpChargesActive) {
      extraPrice = 0;
    }
  }

  return (
    <View style={[styles.container, {
      // flex: 1
      width: windowWidth,
      height: windowHeight,
    }]}>
      <ScrollView style={[{
        flex: 1,
      }]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingLeft: 10,
          paddingRight: 10,
          paddingTop: 5,
        }}
        nestedScrollEnabled={true}>
        {/* <View
          style={{
            flexDirection: "row",
            flex: 1,
            alignContent: "center",
            alignItems: "center",
            marginBottom: 30,
          }}
        >
          <View
            style={[{
              backgroundColor: Colors.secondaryColor,
              width: 60,
              height: 60,
              borderRadius: 10,
            }, menuItem.image ? {

            } : {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }]}
          >
            {menuItem.image
              ?
              <Image
                source={{ uri: menuItem.image }}
                style={{ width: 60, height: 60, borderRadius: 10 }}
              />
              :
              <Icon name="fast-food-outline" size={45} />
            }
          </View>

          <View style={{ marginLeft: 15, width: "80%" }}>
            <Text
              style={{
                fontWeight: "600",
                fontSize: 16,
                textTransform: "uppercase",
              }}
            >
              {menuItem.name}
            </Text>
            <Text
              style={{
                color: Colors.primaryColor,
                fontWeight: "700",
                paddingTop: 5,
                fontSize: 12,
              }}>
              RM{parseFloat(menuItem.price).toFixed(2)}
            </Text>
          </View>
        </View> */}

        <View style={{
          flexDirection: 'row',
          alignContent: 'center',
          alignItems: 'center',
          width: "75%",
          display: 'flex',
          justifyContent: 'flex-start',
          // backgroundColor: 'blue',
          marginBottom: 20,
        }}>
          <View style={[{
            backgroundColor: Colors.secondaryColor,
            // width: 60,
            // height: 60,
            width: isMobile() ? Dimensions.get('window').width * 0.22 : Dimensions.get('window').width * 0.05,
            height: isMobile() ? Dimensions.get('window').width * 0.22 : Dimensions.get('window').width * 0.05,
            borderRadius: 10,
          }, selectedOutletItem && selectedOutletItem.image ? {

          } : {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }]}>
            {selectedOutletItem && selectedOutletItem.image
              ?
              <AsyncImage source={{ uri: selectedOutletItem.image }} item={selectedOutletItem} style={{
                // width: 60,
                // height: 60,
                width: isMobile() ? Dimensions.get('window').width * 0.22 : Dimensions.get('window').width * 0.05,
                height: isMobile() ? Dimensions.get('window').width * 0.22 : Dimensions.get('window').width * 0.05,
                borderRadius: 10
              }} />
              :
              // <Ionicons name="fast-food-outline" size={50} />
              <View style={{
                width: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                height: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Ionicons name="fast-food-outline"
                  // size={45}
                  size={isMobile() ? windowWidth * 0.1 : windowWidth * 0.02}
                />
              </View>
            }
          </View>
          <View style={{
            marginLeft: 14,
            // flexDirection: 'row',
            // flexShrink: 1,
            width: '90%',
            // backgroundColor: 'red',
          }}>
            <Text
              // numberOfLines={1} 
              style={{
                fontSize: 15,
                textTransform:
                  'uppercase',
                fontFamily: "NunitoSans-Bold",
                // flexWrap: 'wrap',
                // flex: 1,
                // flexShrink: 1,    
                // width: '100%',
              }}>{selectedOutletItem && selectedOutletItem.dpName ? selectedOutletItem.dpName : (selectedOutletItem.name ? selectedOutletItem.name : '')}</Text>
            <Text
              // numberOfLines={1} 
              style={{
                fontSize: 15,
                fontFamily: "NunitoSans-Bold",
                paddingTop: 5,
                // flexWrap: 'wrap',
                // flex: 1,
                // flexShrink: 1,    
                // width: '100%',
              }}>{selectedOutletItem && selectedOutletItem.description ? selectedOutletItem.description : ''}</Text>

            <Text style={{
              color: Colors.primaryColor,
              fontFamily: "NunitoSans-Bold",
              paddingTop: 5,
              fontSize: 16,
            }}>RM{
                selectedOutletItem && selectedOutletItem.sku && (overrideItemPriceSkuDict[selectedOutletItem.sku] !== undefined ||
                  overrideCategoryPrice !== undefined)
                  ?
                  (
                    overrideItemPriceSkuDict[selectedOutletItem.sku] && overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice !== undefined
                      ?
                      parseFloat(
                        overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice
                      ).toFixed(2)
                      :
                      parseFloat(
                        overrideCategoryPrice,
                      ).toFixed(2)
                  ) :
                  parseFloat(extraPrice + selectedOutletItem.price).toFixed(2)
              }{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
            </Text>
          </View>
        </View>

        {/* <View style={{ marginBottom: 15 }}>
          <Text style={{ fontWeight: "700", fontSize: 16 }}>
            Choose size
              </Text>
          <View style={{ marginTop: 15, flexDirection: "row" }}>
            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick() }}>
              <Text style={{ alignSelf: "center", color: clicked == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Small</Text>
            </TouchableOpacity>

            <View style={{ width: '5%' }}></View>

            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked1 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick1() }}>
              <Text style={{ alignSelf: "center", color: clicked1 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Medium</Text>
            </TouchableOpacity>

            <View style={{ width: '5%' }}></View>

            <TouchableOpacity style={{
              width: "30%", height: 40, backgroundColor: clicked2 == 1 ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
              borderColor: Colors.descriptionColor,
            }}
              onPress={() => { changeClick2() }}>
              <Text style={{ alignSelf: "center", color: clicked2 == 1 ? Colors.whiteColor : Colors.descriptionColor }}>Big</Text>
            </TouchableOpacity>
          </View>
        </View> */}

        {isLoading ?
          <View>
            <ActivityIndicator color={Colors.primaryColor} size={"large"} />
          </View>
          :
          <>
            {
              (outletsItemAddOnDict[selectedOutletItem.uniqueId]
                ? outletsItemAddOnDict[selectedOutletItem.uniqueId] : []).length > 0
                ?
                <Text
                  style={{
                    color: Colors.blackColor,
                    marginBottom: 10,
                    fontSize: 18,
                    fontFamily: 'NunitoSans-SemiBold',
                  }}
                >
                  Let's make it better?
                </Text>
                :
                <></>
            }
            {sortedVariantAddOnList
              ? sortedVariantAddOnList.map((item, index) => {

                if (item.minSelect !== undefined && item.maxSelect !== undefined) {
                  // means is variant

                  return (
                    <>
                      <View style={{
                        marginBottom: 20,
                      }}
                        onLayout={(event) => {
                          const { width, height, x, y, } = event.nativeEvent.layout;

                          if (item && item.uniqueId) {
                            global.addOnPosYByIdDict[item.uniqueId] = y;
                          }
                        }}
                      >
                        <View style={{ flexDirection: "row", justifyContent: 'space-between' }}>
                          <Text
                            style={{
                              color: addOnVerifiedResultDict[item.uniqueId] ? Colors.tabRed : Colors.primaryColor,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-SemiBold',
                            }}
                          >
                            {item.name ? item.name : 'N/A'}
                          </Text>
                          <Text
                            style={{
                              color: '#282C3F',
                              fontSize: 12,
                              fontFamily: 'NunitoSans-SemiBold',
                              backgroundColor: '#56FD481A',
                              paddingVertical: 2,
                              paddingHorizontal: 10,
                              borderColor: Colors.primaryColor,
                              borderWidth: 1,
                              borderRadius: 5,
                              opacity: 0.7,
                            }}
                          >
                            {item.minSelect === 0 ? 'Optional' : 'Compulsory'}
                          </Text>
                        </View>
                        <Text
                          style={{
                            color: '#86898F',
                            fontSize: 12,
                            fontFamily: 'NunitoSans-SemiBold',
                          }}
                        >
                          {item.maxSelect > 1 ? `(Please select up to ${item.maxSelect} choices)` : ''}
                        </Text>

                        <FlatList
                          horizontal={false}
                          nestedScrollEnabled={true}
                          data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                            return !choiceFilter.isHidden;
                          }) : []}
                          renderItem={dataItem => renderVariants(dataItem, item)}
                          showsHorizontalScrollIndicator={false}
                          style={{
                            marginTop: 10,

                          }}
                        />
                      </View>
                      <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginBottom: 25, opacity: 0.6, borderColor: 'grey' }} />
                    </>
                  );
                }
                else {
                  // means is addon

                  return (
                    <View style={{ marginBottom: 15 }}
                      onLayout={(event) => {
                        const { width, height, x, y, } = event.nativeEvent.layout;

                        if (item && item.uniqueId) {
                          global.addOnPosYByIdDict[item.uniqueId] = y;
                        }
                      }}
                    >
                      <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', }}>
                        <Text
                          style={{
                            color: addOnMinMaxMessageResultDict[item.uniqueId] ? Colors.tabRed : Colors.primaryColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-SemiBold',
                          }}
                        >
                          {item.name ? item.name : 'N/A'}
                        </Text>
                        <Text
                          style={{
                            color: '#a8a8a8',
                            backgroundColor: '#eaeaea',
                            textAlign: 'center',
                            justifyContent: 'center',
                            // fontWeight: "500",
                            // fontSize: 16,
                            marginBottom: 0,
                            // color: Colors.mainTxtColor,
                            // fontWeight: "500",
                            fontSize: 10,
                            fontFamily: 'NunitoSans-SemiBold',
                            marginLeft: 8,
                            borderRadius: 10,
                            paddingHorizontal: 7,
                            paddingVertical: 2,
                          }}
                        >
                          {`${(selectedAddOnIdForChoiceQtyDict[item.uniqueId] && selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty) ? selectedAddOnIdForChoiceQtyDict[item.uniqueId].countedQty : 0} Selected`}
                        </Text>
                      </View>

                      {
                        (item.min > 0 || item.max > 0)
                          ?
                          <Text
                            style={{
                              color: 'red',
                              backgroundColor: '#fffbe8',
                              // backgroundColor: Colors.secondaryColor,
                              textAlign: 'center',
                              justifyContent: 'center',
                              // fontWeight: "500",
                              // fontSize: 16,
                              marginBottom: 0,
                              // color: Colors.mainTxtColor,
                              // fontWeight: "500",
                              fontSize: 13,
                              fontFamily: 'NunitoSans-SemiBold',
                              fontWeight: 'bold',
                              marginTop: 20,
                              padding: 6,
                            }}
                          >
                            {`Required - Select ${item.min} - ${item.max} only`}
                          </Text>
                          :
                          <></>
                      }

                      <FlatList
                        horizontal={false}
                        nestedScrollEnabled={true}
                        data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                          return !choiceFilter.isHidden;
                        }) : []}
                        renderItem={dataItem => renderAddons(dataItem, item)}
                        style={{
                          marginTop: 10,
                        }}
                      />
                    </View>
                  );
                }
              })
              : null}
          </>
        }

        {/* {outletsItemAddOnDict[selectedOutletItem.uniqueId]
          ? outletsItemAddOnDict[selectedOutletItem.uniqueId].map((item, index) => {  
            console.log('outletsItemAddOnDict');
            console.log(outletsItemAddOnDict);
            console.log('outletsItemAddOnChoiceDict');
            console.log(outletsItemAddOnChoiceDict);

            return (
              (item.name !== "" ? <View style={{ marginBottom: 15 }}>
                <Text
                  style={{
                    color: Colors.descriptionColor,
                    fontWeight: "500",
                    fontSize: 16,
                    marginBottom: 15,
                  }}
                >
                  {item.name}
                </Text>
                {outletsItemAddOnChoiceDict[item.uniqueId] && outletsItemAddOnChoiceDict[item.uniqueId].map((item2, index) => {
                  return (
                    (item2.name !== "" ? <View
                      style={{
                        marginBottom: 20,
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {item.leastSelect == 1 ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              marginRight: 10,
                            }}>

                            <TouchableOpacity
                              style={[
                                styles.addBtnSmall,
                                {
                                  backgroundColor:
                                    getOptionQuantity(item2.uniqueId) > 0
                                      ? Colors.primaryColor
                                      : "#e8e9eb",
                                },
                              ]}
                              onPress={() => {
                                console.log("getOptionQuantity(item2.uniqudId)", -getOptionQuantity(item2.uniqueId))
                                if (getOptionQuantity(item2.uniqueId) >= 1) {
                                  addOption(
                                    item2.name,
                                    -getOptionQuantity(item2.uniqueId),
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }
                                else {
                                  addOption(
                                    item2.name,
                                    1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }


                              }}
                            >
                              <Icon1
                                name="check"
                                size={25}
                                color={
                                  getOptionQuantity(item2.uniqueId) > 0
                                    ? Colors.whiteColor
                                    : Colors.descriptionColor
                                }
                              />
                            </TouchableOpacity>

                          </View>
                        ) : (
                            <View
                              style={{
                                flexDirection: 'row',
                                marginRight: 10,
                              }}>
                              <View>
                                <TouchableOpacity
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor:
                                        containsObject(item2.uniqueId) == true
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                    },
                                  ]}
                                  onPress={() => {
                                    addLeastItem(
                                      item2.name,
                                      item2.uniqueId,
                                      item2.price,
                                      1,
                                    );
                                  }}>
                                  <Icon1
                                    name="check"
                                    size={25}
                                    color={
                                      containsObject(item2.uniqueId) == true
                                        ? Colors.whiteColor
                                        : Colors.descriptionColor
                                    }
                                  />
                                </TouchableOpacity>
                              </View>
                            </View>
                          )}
                        <View style={{ width: '55%' }}>
                          <Text style={{ fontSize: 14, fontWeight: '700' }}>
                            {item2.name}
                          </Text>
                        </View>
                        {item.leastSelect == 1 ? (
                          <View style={{ width: '14%', marginLeft: 3 }}>
                            <View style={{ flexDirection: 'row' }}>
                              <TouchableOpacity
                                disabled={false}
                                onPress={() => {
                                  if (item2.quantity > 0) {
                                    item2.quantity - 1;
                                  }
                                  addOption(
                                    item2.name,
                                    -1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }}>
                                <View
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor:
                                        Colors.descriptionColor,
                                    },
                                  ]}>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      fontWeight: '500',
                                      color: Colors.whiteColor,
                                    }}>
                                    -
                                    </Text>
                                </View>
                              </TouchableOpacity>
                              <View
                                style={[
                                  styles.addBtnSmall,
                                  {
                                    backgroundColor: Colors.whiteColor,
                                    borderWidth: StyleSheet.hairlineWidth,
                                    borderColor: Colors.descriptionColor,
                                  },
                                ]}>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    fontWeight: 'bold',
                                    color: Colors.primaryColor,
                                  }}>
                                  {getOptionQuantity(item2.uniqueId)}
                                </Text>
                              </View>
                              <TouchableOpacity
                                disabled={false}
                                onPress={() => {
                                  item2.uniqueId + 1;
                                  addOption(
                                    item2.name,
                                    1,
                                    item2.uniqueId,
                                    item2.price,
                                  );
                                }}>
                                <View
                                  style={[
                                    styles.addBtnSmall,
                                    {
                                      backgroundColor: Colors.primaryColor,
                                    },
                                  ]}>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      fontWeight: '500',
                                      color: Colors.whiteColor,
                                    }}>
                                    +
                                    </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          </View>
                        ) : (
                            <View
                              style={{ width: '14%', marginLeft: 3 }}></View>
                          )}
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        {item.leastSelect == 1 ? (
                          <View style={{ width: '50%' }}>
                            <Text style={{ fontSize: 14, fontWeight: '300' }}>
                              {' '}
                                + RM
                                {(parseFloat(item2.price).toFixed(2) *
                                getOptionQuantity(item2.uniqueId)).toFixed(2)}
                            </Text>
                          </View>
                        ) : (
                            <View style={{ width: '50%' }}>
                              <Text style={{ fontSize: 14, fontWeight: '300' }}>
                                {' '}
                                + RM
                                {parseFloat(
                                  item2.price,
                                ).toFixed(2) * 1}
                              </Text>
                            </View>
                          )}
                      </View>
                    </View> : null)
                  );
                })}
              </View> : null)
            );
          })
          : null} */}

        {
          (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE)
            ?
            <View
              // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              // keyboardVerticalOffset={-windowHeight * 0.2}
              style={{
                marginBottom: 15,
                marginTop: 10,
              }}>
              <Text
                style={{
                  //color: Colors.descriptionColor,
                  // color: Colors.mainTxtColor,
                  // fontWeight: "500",
                  fontSize: 17,
                  fontFamily: 'NunitoSans-SemiBold',
                  marginBottom: 15,
                }}>
                Variable Price:
              </Text>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                // textAlignVertical={'top'}
                style={[styles.textInput, {
                  height: 50,
                  width: '12%',
                }]}
                placeholder="0.00"
                // placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                // onChangeText={(text) => {
                //   // setState({ remark: text });
                //   setRemark(text);
                // }}
                keyboardType='decimal-pad'
                value={variablePrice}
                //iOS
                clearTextOnFocus={true}
                //////////////////////////////////////////////
                //Android
                onFocus={() => {
                  setTemp(variablePrice)
                  setVariablePrice('');
                }}
                ///////////////////////////////////////////////
                //When textinput is not selected
                onEndEditing={() => {
                  if (variablePrice == '') {
                    setVariablePrice(temp);
                  }
                }}
                onChangeText={(text) => {
                  setVariablePrice(parseValidPriceText(text));
                }}
              // multiline={true}
              />
            </View>
            :
            <></>
        }

        <View style={{
          marginBottom: isMobile() ? 120 : 80,
          marginTop: 5,

          opacity: (selectedOutlet && (
            selectedOutlet.rmShowM === undefined
            ||
            selectedOutlet.rmShowM === true
          )) ? 100 : 0,
        }}>
          <Text
            style={{
              color: Colors.descriptionColor,
              // color: Colors.mainTxtColor,
              // fontWeight: "500",
              fontSize: 17,
              fontFamily: 'NunitoSans-SemiBold',
              marginBottom: 15,
            }}
          >
            Special Remarks:
          </Text>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={[styles.textInput, {
              height: isMobile() ? windowWidth * 0.35 : windowWidth * 0.08,
            }]}
            placeholder="eg: no onions"
            onChangeText={(text) => {
              // setState({ remark: text });
              setRemark(text);
            }}
            value={remark}
            multiline={true}
          />
        </View>




      </ScrollView>
      {/* {cartIcon ?
        <Draggable
          shouldReverse={reverse}
          renderSize={100}
          renderColor={Colors.secondaryColor}
          isCircle
          x={280}
          y={500}
          onShortPressRelease={() => {
            goToCart();
          }}
        >
          <View style={{ width: 60, height: 60, justifyContent: "center" }}>
            <View style={{ alignSelf: "center" }}>
              <Icon name="cart-outline" size={45} />
            </View>
            <View style={styles.cartCount}>
              <Text style={{ color: Colors.whiteColor, fontSize: 10 }}>
                {Cart.getCartItem().length}
              </Text>
            </View>
          </View>
        </Draggable>
        : null} */}
      <View style={{
        position: "absolute",
        bottom: 60,
        // bottom: 'env(safe-area-inset-bottom)',
        left: 0,
        right: 0,
        flexDirection: 'row',
        backgroundColor: Colors.whiteColor,
        width: windowWidth,
        height: 60,
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 7,
        justifyContent: 'space-between',
      }}>
        <View style={{ flexDirection: "row", borderWidth: 1, borderRadius: 25, borderColor: Colors.primaryColor, alignItems: 'center', width: '25%', justifyContent: 'space-around' }}>
          <TouchableOpacity
            onPress={() => {
              if (global.outletName) {
                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_QUANTITY_CLICK, {
                  eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_QUANTITY_CLICK,

                  outletName: global.outletName ? `${global.outletName} (Web)` : '',

                  webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                });
              }

              setModalQuantity(modalQuantity - 1 >= 1 ? modalQuantity - 1 : 1);
            }}>
            <View
              testID="menuItemDetailMinus"
              style={[
                styles.addBtn,
                // { width: windowWidth * 0.075, },
              ]}
            >
              <FontAwesome name="minus"
                color={Colors.primaryColor}
                size={isMobile() ? 12 : 12}
              />
            </View>
          </TouchableOpacity>
          <View
            style={[
              styles.addBtn,
              // { width: windowWidth * 0.075, },
            ]}
          >
            <Text
              testID="menuItemDetailQuantity"
              style={{
                fontSize: 18,
                fontFamily: "NunitoSans-Bold",
                color: Colors.primaryColor,
                marginBottom: isMobile() ? 0 : 3,
              }}>
              {modalQuantity}
            </Text>
          </View>

          <TouchableOpacity
            onPress={() => {
              if (global.outletName) {
                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_QUANTITY_CLICK, {
                  eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_QUANTITY_CLICK,

                  outletName: global.outletName ? `${global.outletName} (Web)` : '',

                  webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                });
              }

              setModalQuantity(modalQuantity + 1);
            }}
            style={{}}
          >
            <View
              testID="menuItemDetailPlus"
              style={[
                styles.addBtn,
                {
                  // left: -1,
                  // width: windowWidth * 0.075,
                },
              ]}
            >
              <FontAwesome name="plus"
                color={Colors.primaryColor}
                size={isMobile() ? 12 : 12}
              />
            </View>
          </TouchableOpacity>
        </View>

        <View style={{ width: '70%', backgroundColor: Colors.primaryColor, alignItems: 'center', borderRadius: 20, }}>
          <TouchableOpacity
            style={{ width: '100%', justifyContent: 'center', alignItems: 'center' }}
            onPress={async () => {
              if (global.outletName) {
                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADD_CLICK, {
                  eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADD_CLICK,

                  outletName: global.outletName ? `${global.outletName} (Web)` : '',

                  webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                });
              }

              if (addOnVerified && addOnMinMaxMessage.length <= 0 && !isLoading) {
                if (!isOrdering) {
                  CommonStore.update((s) => {
                    s.isOrdering = true;
                    s.isLoading = true;
                  });

                  var newCartItems = [];

                  if (selectedOutletItem) {
                    var tempCartItemChoices = {};

                    if (outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
                      // const tempOutletsItemAddOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId];

                      let tempOutletsItemAddOnList =
                        [...outletsItemAddOnDict[selectedOutletItem.uniqueId]];

                      tempOutletsItemAddOnList.sort((a, b) => {
                        return (
                          ((a.orderIndex !== undefined)
                            ? a.orderIndex
                            : tempOutletsItemAddOnList.length) -
                          ((b.orderIndex !== undefined)
                            ? b.orderIndex
                            : tempOutletsItemAddOnList.length)
                        );
                      })

                      for (var i = 0; i < tempOutletsItemAddOnList.length; i++) {
                        // check against the default item add on list

                        const tempAddOn = tempOutletsItemAddOnList[i];

                        if (selectedOutletItemAddOn[tempAddOn.uniqueId] !== undefined) {
                          // means this addon got choices selected before (could also means deselected, so need checks further as shown below)

                          const tempAddOnSelectedObjList = Object.entries(
                            selectedOutletItemAddOn[tempAddOn.uniqueId],
                          ).map(([key, value]) => ({ key: key, value: value }));

                          for (var j = 0; j < tempAddOnSelectedObjList.length; j++) {
                            if (tempAddOnSelectedObjList[j].value === true) {
                              // means this addon's choice is selected

                              tempCartItemChoices[tempAddOnSelectedObjList[j].key] = true;
                            }
                            else {
                              tempCartItemChoices[tempAddOnSelectedObjList[j].key] = false;
                            }
                          }
                        }
                      }
                    }

                    ///////////////////////////////////////////////////

                    // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
                    const newSelectedOutletItemAddOnList = Object.entries(
                      newSelectedOutletItemAddOnDetails,
                    ).map(([key, value]) => ({ key: key, value: value })).sort((a, b) => a.value.choiceName.localeCompare(b.value.choiceName));

                    var addOnGroupList = [];

                    for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
                      if (newSelectedOutletItemAddOnList[i].value &&
                        newSelectedOutletItemAddOnList[i].value.quantity > 0) {
                        const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

                        if (addOnTemp) {
                          addOnGroupList.push(addOnTemp);
                        }
                      }
                    }

                    ///////////////////////////////////////////////////

                    if (onUpdatingCartItem) {
                      // update existing cart item

                      var updateCartItemIndex = 0;

                      for (var i = 0; i < cartItems.length; i++) {
                        if (cartItems[i].itemId === onUpdatingCartItem.itemId &&
                          cartItems[i].cartItemDate === onUpdatingCartItem.cartItemDate) {
                          updateCartItemIndex = i;
                        }
                      }

                      // var newCartItems = [];

                      if (modalQuantity <= 0) {
                        newCartItems = [
                          ...cartItems.slice(0, updateCartItemIndex),
                          ...cartItems.slice(updateCartItemIndex + 1),
                        ];
                      }
                      else {
                        newCartItems = [
                          ...cartItems.slice(0, updateCartItemIndex),
                          {
                            priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity),

                            itemId: selectedOutletItem.uniqueId,
                            choices: tempCartItemChoices,
                            remarks: remark,
                            fireOrder: false,
                            quantity: modalQuantity,
                            cartItemDate: Date.now(), // hmm need update or use existing?

                            addOnGroupList: addOnGroupList,

                            itemSku: selectedOutletItem.sku,
                            categoryId: selectedOutletItem.categoryId,
                            printerAreaList: selectedOutletItem.printerAreaList || [],
                            printingTypeList: selectedOutletItem.printingTypeList || null,

                            isDocket: selectedOutletItem.isDocket || false,
                            printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                            ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                              priceVariable: parseFloat(variablePrice),
                            },

                            priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                            unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                            itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                            ...selectedOutletItem.upsellingCampaignId && {
                              priceUpselling: selectedOutletItem.priceUpselling,
                              upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                              upc: selectedOutletItem.upc,
                            },
                          },
                          ...cartItems.slice(updateCartItemIndex + 1),
                        ];
                      }

                      if (newCartItems.length > 0) {
                        safelyExecuteIdb(() => {
                          // AsyncStorage.setItem(`${firebaseUid}.cartItems`, JSON.stringify(newCartItems));
                          idbSet(`cartItems`, JSON.stringify(newCartItems));

                          // AsyncStorage.setItem(`${firebaseUid}.cartOutletId`, selectedOutlet.uniqueId);
                          idbSet(`cartOutletId`, selectedOutlet.uniqueId);
                        });
                      }
                      else {
                        safelyExecuteIdb(() => {
                          // AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
                          idbDel(`cartItems`);

                          // AsyncStorage.removeItem(`${firebaseUid}.cartOutletId`);
                          idbDel(`cartOutletId`);
                        });
                      }

                      CommonStore.update(s => {
                        // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                        s.cartItems = newCartItems;

                        if (newCartItems.length <= 0) {
                          s.cartOutletId = null;
                        }
                      });

                      PaymentStore.update(s => {
                        s.cartItemsPayment = newCartItems;
                        s.outletIdPayment = selectedOutlet.uniqueId;
                        s.dateTimePayment = Date.now();
                        s.orderTypePayment = orderType;
                      });

                      console.log('cartItemsPayment');
                      console.log(newCartItems)
                      console.log('outletIdPayment');
                      console.log(selectedOutlet.uniqueId)
                      console.log('dateTimePayment');
                      console.log(Date.now())
                      console.log('orderTypePayment');
                      console.log(orderType)
                    }
                    else {
                      // add to cart

                      if (modalQuantity <= 0) {
                        window.confirm('Info\n\nPlease add one or more items');

                        return;
                      }

                      newCartItems = [
                        ...cartItems,
                        {
                          priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity),

                          itemId: selectedOutletItem.uniqueId,
                          choices: tempCartItemChoices,
                          remarks: remark,
                          fireOrder: false,
                          quantity: modalQuantity,
                          cartItemDate: Date.now(),

                          addOnGroupList: addOnGroupList,

                          itemSku: selectedOutletItem.sku,
                          categoryId: selectedOutletItem.categoryId,
                          printerAreaList: selectedOutletItem.printerAreaList || [],
                          printingTypeList: selectedOutletItem.printingTypeList || null,

                          isDocket: selectedOutletItem.isDocket || false,
                          printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,

                          ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                            priceVariable: parseFloat(variablePrice),
                          },

                          priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                          unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,

                          itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,

                          ...selectedOutletItem.upsellingCampaignId && {
                            priceUpselling: selectedOutletItem.priceUpselling,
                            upsellingCampaignId: selectedOutletItem.upsellingCampaignId,

                            upc: selectedOutletItem.upc,
                          },
                        },
                      ];

                      safelyExecuteIdb(() => {
                        // AsyncStorage.setItem(`${firebaseUid}.cartItems`, JSON.stringify(newCartItems));
                        idbSet(`cartItems`, JSON.stringify(newCartItems));
                      });

                      AsyncStorage.setItem(`${firebaseUid}.cartOutletId`, selectedOutlet.uniqueId);

                      CommonStore.update(s => {
                        s.cartItemChoices = {
                          ...cartItemChoices,
                          [selectedOutletItem.uniqueId]: {
                            ...tempCartItemChoices,
                          },
                        };

                        // s.cartItems = new Set(cartItems).add(selectedOutletItem.uniqueId);
                        s.cartItems = newCartItems;
                      });

                      PaymentStore.update(s => {
                        s.cartItemsPayment = newCartItems;
                        s.outletIdPayment = selectedOutlet.uniqueId;
                        s.dateTimePayment = Date.now();
                        s.orderTypePayment = orderType;
                      });

                      console.log('cartItemsPayment');
                      console.log(newCartItems)
                      console.log('outletIdPayment');
                      console.log(selectedOutlet.uniqueId)
                      console.log('dateTimePayment');
                      console.log(Date.now())
                      console.log('orderTypePayment');
                      console.log(orderType)

                      console.log('cartItemChoices');
                      console.log({
                        ...cartItemChoices,
                        [selectedOutletItem.uniqueId]: {
                          ...tempCartItemChoices,
                        },
                      });
                      console.log('cartItems');
                      console.log(newCartItems);
                    }
                  }

                  if (selectedOutletTableId.length > 0) {
                    await updateUserCart(newCartItems);
                  }

                  CommonStore.update(s => {
                    s.cartOutletId = selectedOutlet.uniqueId;
                  });

                  if (onUpdatingCartItem) {

                    const subdomain = await AsyncStorage.getItem('latestSubdomain');

                    await AsyncStorage.setItem('onUpdatingCartItem', '1');

                    if (!subdomain) {
                      linkTo && linkTo(`${prefix}/outlet/cart`);
                    }
                    else {
                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                    }

                    if (global.redirectFromPage === 'NoVoucher Cart') {
                      global.redirectFromPage = '';

                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/novoucher-cart`);
                    }
                    else {
                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                    }

                    // linkTo(`${prefix}/outlet/cart`);
                  }
                  else {
                    if (global.isFromRecommendedItems) {
                      global.isFromRecommendedItems = false;

                      const subdomain = await AsyncStorage.getItem('latestSubdomain');

                      if (!subdomain) {
                        linkTo && linkTo(`${prefix}/outlet/cart`);
                      }
                      else {
                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                      }

                    }
                    else {
                      const subdomain = await AsyncStorage.getItem('latestSubdomain');

                      if (!subdomain) {
                        linkTo && linkTo(`${prefix}/outlet/menu`);
                      }
                      else {
                        // if (subdomain === 'hominsan-ss15' || subdomain === 'hominsanttdi') {
                        if (true) {
                          if (
                            (upsellingCampaignsAfterCart && upsellingCampaignsAfterCart.length > 0)
                            ||
                            (global.upsellingCampaignsAfterCart && global.upsellingCampaignsAfterCart.length > 0)
                          ) {
                            CommonStore.update((s) => {
                              s.currPage = "UpsellMenu";
                            });

                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/upsell-menu`);
                          }
                          else {
                            if (global.redirectFromPage === 'Reservation') {
                              global.redirectFromPage = '';

                              linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
                            }
                            else {
                              linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                            }
                          }
                        }
                        else {
                          linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                        }
                      }

                      // linkTo(`${prefix}/outlet/menu`); 
                    }
                  }
                  CommonStore.update((s) => {
                    s.menuItemDetailModal = false;
                  });

                  global.menuItemDetailModal = false;

                  // navigation.goBack();

                  setTimeout(() => {
                    CommonStore.update((s) => {
                      s.isOrdering = false;
                      s.isLoading = false;
                    });
                  }, 1000);
                }
              }
              else {
                console.log(addOnVerifiedResultList);

                if (addOnVerifiedResultList.length > 0) {
                  // variants checking

                  let addOnVerifiedResult = addOnVerifiedResultList[0];
                  if (typeof global.addOnPosYByIdDict[addOnVerifiedResult.uniqueId] === 'number') {
                    setAddOnMinMaxMessageResultDict({});

                    setAddOnVerifiedResultDict({
                      [addOnVerifiedResult.uniqueId]: true,
                    });

                    addOnScrollViewRef.current.scrollTo({ y: global.addOnPosYByIdDict[addOnVerifiedResult.uniqueId] - 50, animated: true });
                  }
                }
                else if (addOnMinMaxMessageResultList.length > 0) {
                  // addons checking

                  let addOnMinMaxMessageResult = addOnMinMaxMessageResultList[0];
                  if (typeof global.addOnPosYByIdDict[addOnMinMaxMessageResult.uniqueId] === 'number') {
                    setAddOnVerifiedResultDict({});

                    setAddOnMinMaxMessageResultDict({
                      [addOnMinMaxMessageResult.uniqueId]: true,
                    });

                    addOnScrollViewRef.current.scrollTo({ y: global.addOnPosYByIdDict[addOnMinMaxMessageResult.uniqueId] - 50, animated: true });
                  }
                }

                alert(`Info, Please select your choice before proceed.\n\n${addOnMinMaxMessage}`)

                // CommonStore.update(s => {
                //   s.alertObj = {
                //     title: 'Info',
                //     message: `Please select your choice before proceed.\n\n${addOnMinMaxMessage}`,
                //   };
                // });
              }
            }}>
            <View style={{
              paddingVertical: 12, flexDirection: 'row', alignItems: 'center'
            }}>
              <Text
                testID="addToCart"
                style={{
                  color: "#ffffff",
                  fontSize: 16,
                  // fontWeight: "bold",
                  fontFamily: 'NunitoSans-Regular',
                  borderRightWidth: 1,
                  borderColor: Colors.whiteColor,
                  paddingRight: 7,
                }}
              >
                {onUpdatingCartItem ? 'Update' : 'Add'}
              </Text>
              <Text style={{
                color: "#ffffff",
                fontSize: 16,
                // fontWeight: "bold",
                fontFamily: 'NunitoSans-Bold',
                paddingLeft: 7,
              }}>
                {'RM '}{(
                  ((selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) ? (parseFloat(variablePrice)) : ((totalPrice + addOnPrice) * modalQuantity))
                ).toFixed(2)}{selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` for ${modalQuantity}${UNIT_TYPE_SHORT[selectedOutletItem.unitType]}` : ''}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>

  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    padding: 16,
  },
  floatCartBtn: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  textInput: {
    // height: 100,
    height: isMobile() ? Dimensions.get('window').width * 0.35 : Dimensions.get('window').width * 0.08,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 12,

    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
    // color: Colors.descriptionColor,
    textAlignVertical: 'top',
    paddingVertical: 15,
  },
  addBtn: {
    // width: 20,
    height: 45,

    display: 'flex',
    justifyContent: "center",
    alignItems: "center",

    borderColor: 'transparent',
  },
  addBtnSmall: {
    backgroundColor: Colors.primaryColor,
    width: 25,
    height: 25,
    justifyContent: "center",
    alignItems: "center",
  },
  cartCount: {
    position: "absolute",
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  confirmBox: {
    width: "70%",
    height: "40%",
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: "100%",
    width: "100%",
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  addBtn1: {
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default MenuItemDetailsScreen;
