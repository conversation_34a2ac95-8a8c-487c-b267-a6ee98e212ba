// import firestore from '@react-native-firebase/firestore';
// import storage from '@react-native-firebase/storage';
// import auth from '@react-native-firebase/auth';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit, onSnapshot, orderBy, startAt, endAt, createDoc, updateDoc, setDoc, doc } from "firebase/firestore";
import { getStorage, ref, getDownloadURL } from "firebase/storage";
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import * as User from './User';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { displayNearbyPromotionNotification } from './notifications';
// import EncryptedStorage from 'react-native-encrypted-storage';
import * as geofire from 'geofire-common';
import moment from 'moment';
import { CHARGES_TYPE, MERCHANT_VOUCHER_STATUS, ORDER_TYPE, ORDER_TYPE_DETAILS, ORDER_TYPE_SUB, OUTLET_SHIFT_STATUS, USER_ORDER_STATUS, VERIFALIA_STATUS } from '../constant/common';
import { LOYALTY_CAMPAIGN_TYPE } from "../constant/loyalty";
import { APPLY_DISCOUNT_PER, APPLY_DISCOUNT_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { prefix } from '../constant/env';
import API from '../constant/API';
import { TempStore } from "../store/tempStore";
import BigNumber from "bignumber.js";
import ApiClient from './ApiClient';
import { LOYALTY_PROMOTION_TYPE } from "../constant/loyalty";
import { USER_QUEUE_STATUS } from '../constant/common';
// import firebase.messaging() from '@react-native-firebase/firebase.messaging()';

import secureLocalStorage from "react-secure-storage";
import { OutletStore } from "../store/outlet";

var routeFrom = null;
var navFrom = null;

export function getRouteFrom() {
    return routeFrom;
};

export function setRouteFrom(param) {
    routeFrom = param;
};

export function getNavFrom() {
    return navFrom;
};

export function setNavFrom(param) {
    navFrom = param;
};

export const validateEmail = async (verifalia, email) => {
    if (verifalia) {
        try {
            const validation = await verifalia
                .emailValidations
                .submit(email, true);

            console.log(validation.entries);

            return validation.entries[0].classification;
        }
        catch (ex) {
            return VERIFALIA_STATUS.API_NOT_AVAILABLE;
        }
    }
};

export const calculateDeliveryFees = (outlet, lat, lng) => {
    var radlat1 = (Math.PI * outlet.lat) / 180;
    var radlat2 = (Math.PI * lat) / 180;
    var theta = outlet.lng - lng;
    var radtheta = (Math.PI * theta) / 180;
    var dist =
        Math.sin(radlat1) * Math.sin(radlat2) +
        Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
    if (dist > 1) {
        dist = 1;
    }
    dist = Math.acos(dist);
    dist = (dist * 180) / Math.PI;
    dist = dist * 60 * 1.1515;

    if (dist) {
        dist = dist * 1.609344;
        dist = dist.toFixed(1);
    }

    var deliveryFees = dist * 2;

    /////////////////////////
    // lalamove adjustment

    deliveryFees = deliveryFees * 2 / 3;

    /////////////////////////

    return deliveryFees.toFixed(2);
};

export const listenToUserChanges = async (firebaseUid, email, userPhone, outletId) => {
    // const firebaseUid = await AsyncStorage.getItem('firebaseUid');
    // const merchantId = await AsyncStorage.getItem('merchantId');
    // const role = await AsyncStorage.getItem('role');

    // console.log(firebaseUid);
    // console.log(merchantId);
    // console.log(role);

    // Get user details
    // firebase.firestore()
    //     .collection(Collections.User)
    //     .where('firebaseUid', '==', firebaseUid)
    //     .onSnapshot(snapshot => {

    // 2024-03-25 - no need first
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.User),
    //         where('firebaseUid', '==', firebaseUid),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.User} changed!`);

    //         if (!snapshot.empty) {
    //             const record = snapshot.docs[0].data();

    //             UserStore.update(s => {
    //                 s.avatar = record.avatar;
    //                 s.dob = record.dob;
    //                 s.email = record.email;
    //                 s.gender = record.gender;
    //                 s.name = record.name;
    //                 s.number = record.number;
    //                 s.outletId = record.outledId;
    //                 s.race = record.race;
    //                 s.state = record.state;
    //                 s.uniqueName = record.uniqueName;
    //                 s.updatedAt = record.updatedAt;

    //                 // s.userGroups = record.userGroups || ['EVERYONE'];
    //             });
    //         }
    //     });

    // EncryptedStorage.removeItem('paymentsDict');

    // causing bugs in ios, disabled first
    // var paymentsDictRaw = await EncryptedStorage.getItem('paymentsDict');
    // var paymentsDict = {};
    // var tempDefaultUserPaymentPointer = null;

    // if (paymentsDictRaw !== null) {
    //     paymentsDict = JSON.parse(paymentsDictRaw);

    //     var defaultUserPaymentPointer = await EncryptedStorage.getItem('defaultUserPaymentPointer');

    //     if (defaultUserPaymentPointer === null) {
    //         // skip
    //     }
    //     else {
    //         if (paymentsDict[defaultUserPaymentPointer]) {
    //             tempDefaultUserPaymentPointer = defaultUserPaymentPointer;
    //         }
    //     }
    // }

    // const tempPaymentPointers = Object.entries(paymentsDict).map(([key, value]) => key);

    // UserStore.update(s => {
    //     s.userPaymentPointers = tempPaymentPointers;
    //     s.selectedUserPaymentPointer = tempDefaultUserPaymentPointer;
    // });

    // firebase.firestore()
    //     .collection(Collections.UserAddress)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(async snapshot => {

    // 2023-09-11 - No longer used
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserAddress),
    //         where('userId', '==', firebaseUid),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserAddress} changed!`);

    //         if (!snapshot.empty) {
    //             var tempUserAddresses = [];
    //             var tempSelectedUserAddress = null;

    //             var defaultUserAddressId = await AsyncStorage.getItem('defaultUserAddressId');

    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 tempUserAddresses.push(record);

    //                 if (defaultUserAddressId !== null && defaultUserAddressId === record.uniqueId) {
    //                     tempSelectedUserAddress = record;
    //                 }
    //             }
    //             if (defaultUserAddressId === null) {
    //                 tempSelectedUserAddress = tempUserAddresses[0];
    //             }

    //             UserStore.update(s => {
    //                 s.userAddresses = tempUserAddresses;
    //                 s.selectedUserAddress = tempSelectedUserAddress;
    //             });
    //         }
    //     });

    // firebase.firestore()
    //     .collection(Collections.UserReservation)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserReservation),
            where('userId', '==', firebaseUid),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserReservation} changed!`);

            var tempUserReservations = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    tempUserReservations.push(record);
                }
            }

            CommonStore.update(s => {
                s.userReservations = tempUserReservations;
            });
        });

    // firebase.firestore()
    //     .collection(Collections.UserQueue)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserQueue),
            where('userId', '==', firebaseUid),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserQueue} changed!`);

            var tempUserQueues = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    tempUserQueues.push(record);
                }
            }

            CommonStore.update(s => {
                s.userQueues = tempUserQueues;
            });
        });

    // firebase.firestore()
    //     .collection(Collections.UserRing)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(snapshot => {

    // 2023-09-11 - No longer used
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserRing),
    //         where('userId', '==', firebaseUid),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserRing} changed!`);

    //         if (!snapshot.empty) {
    //             var tempUserRings = [];

    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 tempUserRings.push(record);
    //             }

    //             CommonStore.update(s => {
    //                 s.userRings = tempUserRings;
    //             });
    //         }
    //     });

    // firebase.firestore()
    //     .collection(Collections.UserOrder)
    //     .where('userId', '==', firebaseUid)
    //     // .orderBy('createdAt', 'desc')
    //     // .limit(30)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserOrder),
            where('userId', '==', firebaseUid),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserOrder} changed!`);

            var tempUserOrders = [];

            // if (!snapshot.empty) {
            if (snapshot && !snapshot.empty) {

                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    // if (outletsDict[record.outletId] === undefined) {
                    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                    //         .where('uniqueId', '==', record.outletId)
                    //         .limit(1)
                    //         .get();

                    //     if (!outletSnapshot.empty) {
                    //         const outlet = outletSnapshot.docs[0].data();

                    //         outletsDict[outlet.uniqueId] = outlet;

                    //         if (merchantsDict[outlet.merchantId] === undefined) {
                    //             const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
                    //                 .where('uniqueId', '==', outlet.merchantId)
                    //                 .limit(1)
                    //                 .get();

                    //             if (!merchantSnapshot.empty) {
                    //                 const merchant = merchantSnapshot.docs[0].data();

                    //                 merchantsDict[merchant.uniqueId] = merchant;
                    //             }
                    //         }
                    //     }
                    // }

                    tempUserOrders.push(record);
                }

                tempUserOrders.sort((a, b) => moment(b.createdAt) - moment(a.createdAt))
            }

            CommonStore.update(s => {
                s.userOrders = tempUserOrders;
            });
        });

    // not listen to userVoucherRedemption, to save firestore reads
    // firebase.firestore()
    //     .collection(Collections.MerchantVoucher)
    //     // .where('startAt', '<=', moment().valueOf())
    //     // .where('endAt', '>', moment().valueOf())
    //     .where('status', '==', MERCHANT_VOUCHER_STATUS.ACTIVE)
    //     .onSnapshot(async snapshot => {

    // 2023-09-11 - No longer used
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.MerchantVoucher),
    //         where('status', '==', MERCHANT_VOUCHER_STATUS.ACTIVE),
    //     ),
    //     async (snapshot) => {
    //         // console.log(`${Collections.MerchantVoucher} changed!`);

    //         if (!snapshot.empty) {
    //             var merchantVouchersDict = [];
    //             var voucherIdValidList = [];

    //             // const userVoucherRedemptionSnapshot = await firebase.firestore().collection(Collections.UserVoucherRedemption)
    //             //     .where('userId', '==', firebaseUid)
    //             //     .limit(1)
    //             //     .get();

    //             const userVoucherRedemptionSnapshot = await getDocs(
    //                 query(
    //                     collection(global.db, Collections.UserVoucherRedemption),
    //                     where('userId', '==', firebaseUid),
    //                     limit(1),
    //                 )
    //             );

    //             if (!userVoucherRedemptionSnapshot.empty) {
    //                 const userVoucherRedemption = userVoucherRedemptionSnapshot.docs[0].data();

    //                 var voucherIdRedemptionList = userVoucherRedemption.redemptions;

    //                 for (var i = 0; i < snapshot.size; i++) {
    //                     const record = snapshot.docs[i].data();

    //                     // console.log('moment(record.startDate).isSameOrBefore(moment())');
    //                     // console.log(moment(record.startDate).isSameOrBefore(moment()));
    //                     // console.log('moment(record.endDate).isAfter(moment())');
    //                     // console.log(moment(record.endDate).isAfter(moment()));

    //                     if (voucherIdRedemptionList.includes(record.uniqueId) ||
    //                         moment(record.startDate).isAfter(moment()) ||
    //                         moment(record.endDate).isSameOrBefore(moment())) {
    //                         // means redeemed by user before, skip for now
    //                     }
    //                     else {
    //                         voucherIdValidList.push(record.uniqueId);

    //                         merchantVouchersDict[record.uniqueId] = record;
    //                     }
    //                 }

    //                 // console.log('voucherIdValidList');
    //                 // console.log(voucherIdValidList);
    //                 // console.log('voucherIdRedemptionList');
    //                 // console.log(voucherIdRedemptionList);
    //                 // console.log('merchantVouchersDict');
    //                 // console.log(merchantVouchersDict);

    //                 UserStore.update(s => {
    //                     s.voucherIdValidList = voucherIdValidList;
    //                     s.voucherIdRedemptionList = voucherIdRedemptionList;
    //                 });

    //                 CommonStore.update(s => {
    //                     s.merchantVouchersDict = merchantVouchersDict;
    //                 });
    //             }
    //         }
    //     });

    // firebase.firestore()
    //     .collection(Collections.UserFavoriteOutlet)
    //     // .where('startAt', '<=', moment().valueOf())
    //     // .where('endAt', '>', moment().valueOf())
    //     .where('userId', '==', firebaseUid)
    //     .limit(1)
    //     .onSnapshot(async snapshot => {

    // 2023-09-11 - No longer used
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserFavoriteOutlet),
    //         where('userId', '==', firebaseUid),
    //         limit(1),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserFavoriteOutlet} changed!`);

    //         var userFavoriteOutletDict = {};

    //         if (!snapshot.empty) {
    //             const record = snapshot.docs[0].data();

    //             userFavoriteOutletDict = record.outletDict;
    //         }

    //         CommonStore.update(s => {
    //             s.userFavoriteOutletDict = userFavoriteOutletDict;
    //         });
    //     });

    // firebase.firestore()
    //     .collection(Collections.UserLoyalty)
    //     // .where('startAt', '<=', moment().valueOf())
    //     // .where('endAt', '>', moment().valueOf())
    //     .where('userId', '==', firebaseUid)
    //     .limit(1)
    //     .onSnapshot(async snapshot => {

    // 2023-09-11 - No longer used
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserLoyalty),
    //         where('userId', '==', firebaseUid),
    //         limit(1),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserLoyalty} changed!`);

    //         var userLoyalty = {
    //             balance: 0,
    //         };

    //         if (!snapshot.empty) {
    //             const record = snapshot.docs[0].data();

    //             userLoyalty = record;
    //         }

    //         CommonStore.update(s => {
    //             s.userLoyalty = userLoyalty;
    //         });
    //     });

    // firebase.firestore()
    //     .collection(Collections.BeerDocket)
    //     // .where('merchantId', '==', merchantId)
    //     .onSnapshot(snapshot => {

    onSnapshot(
        query(
            collection(global.db, Collections.BeerDocket),
            // where('merchantId', '==', merchantId),
            limit(10),
        ),
        async (snapshot) => {
            console.log(`${Collections.BeerDocket} changed!`);

            var beerDockets = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    beerDockets.push(record);
                }
            }

            CommonStore.update(s => {
                s.beerDockets = beerDockets;
            });
        });

    // firebase.firestore()
    //     .collection(Collections.UserBeerDocket)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(snapshot => {

    onSnapshot(
        query(
            collection(global.db, Collections.UserBeerDocket),
            where('userId', '==', firebaseUid),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserBeerDocket} changed!`);

            var beerDocketsRedemptions = [];
            var beerDocketsRedemptionsBDDict = {};

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    beerDocketsRedemptions.push(record);
                    beerDocketsRedemptionsBDDict[record.beerDocketId] = record;
                }
            }

            CommonStore.update(s => {
                s.beerDocketsRedemptions = beerDocketsRedemptions;
                s.beerDocketsRedemptionsBDDict = beerDocketsRedemptionsBDDict;
            });
        });

    // firebase.firestore()
    //     .collection(Collections.UserPointsTransaction)
    //     .where('userId', '==', firebaseUid)
    //     .onSnapshot(snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserPointsTransaction),
            // where('userId', '==', firebaseUid),
            //////////////////////////////////////////////////////////////////////////////
            where('email', '==', email),
            where('merchantId', '==', outletId.merchantId),
            where('deletedAt', '==', null),
            //////////////////////////////////////////////////////////////////////////////

        ),
        async (snapshot) => {
            if (email == "")
                return;

            console.log(`${Collections.UserPointsTransaction} changed!`);

            // var userPointsTransactions = [];
            // var userPointsBalance = 0;
            //////////////////////////////////////////////////////////////////////////////

            var selectedCustomerPointsTransactionsEmail = [];
            var selectedCustomerPointsBalanceEmail = 0;
            //////////////////////////////////////////////////////////////////////////////

            // if (!snapshot.empty) {
            //     for (var i = 0; i < snapshot.size; i++) {
            //         const record = snapshot.docs[i].data();

            //         userPointsTransactions.push(record);

            //         userPointsBalance += record.amount;
            //     }
            // }

            //////////////////////////////////////////////////////////////////////////////
            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (!isNaN(record.amount) && typeof record.amount === 'number') {
                        selectedCustomerPointsTransactionsEmail.push(record);
                        selectedCustomerPointsBalanceEmail += record.amount;
                    }
                }
            }

            selectedCustomerPointsTransactionsEmail.sort(
                (a, b) => b.createdAt - a.createdAt,
            );

            if (snapshot) {
                UserStore.update((s) => {
                    s.selectedCustomerPointsTransactionsEmail = selectedCustomerPointsTransactionsEmail;
                    s.selectedCustomerPointsBalanceEmail = selectedCustomerPointsBalanceEmail;
                });
            }
            //////////////////////////////////////////////////////////////////////////////

            // UserStore.update(s => {
            //     s.userPointsTransactions = userPointsTransactions;
            //     s.userPointsBalance = userPointsBalance;
            // });
        });
    onSnapshot(
        query(
            collection(global.db, Collections.UserPointsTransaction),
            // where('userId', '==', firebaseUid),
            where('phone', '==', userPhone),
            where('merchantId', '==', outletId.merchantId),
            where('deletedAt', '==', null),

        ),
        async (snapshot) => {
            if (userPhone == "")
                return;
            console.log(`${Collections.UserPointsTransaction} changed!`);

            var selectedCustomerPointsTransactionsPhone = [];
            var selectedCustomerPointsBalancePhone = 0;
            //////////////////////////////////////////////////////////////////////////////

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (!isNaN(record.amount) && typeof record.amount === 'number') {
                        selectedCustomerPointsTransactionsPhone.push(record);
                        selectedCustomerPointsBalancePhone += record.amount;
                    }
                }
            }

            selectedCustomerPointsTransactionsPhone.sort(
                (a, b) => b.createdAt - a.createdAt,
            );

            if (snapshot) {
                UserStore.update((s) => {
                    s.selectedCustomerPointsTransactionsPhone =
                        selectedCustomerPointsTransactionsPhone;
                    s.selectedCustomerPointsBalancePhone = selectedCustomerPointsBalancePhone;
                });
            }
        });

    // firebase.firestore()
    //     .collection(Collections.UserLoyaltyStamp)
    //     .where('userId', '==', firebaseUid)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserLoyaltyStamp),
            // where('userId', '==', firebaseUid),
            where('phone', '==', userPhone),
            where('outletId', '==', outletId.uniqueId),
            where('deletedAt', '==', null)
        ),
        async (snapshot) => {
            console.log(`${Collections.UserLoyaltyStamp} changed!`);

            var userLoyaltyStamps = [];
            var userLoyaltyStampsDict = {};
            // var beerDocketsRedemptionsBDDict = {};

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    userLoyaltyStamps.push(record);
                    userLoyaltyStampsDict[record.loyaltyStampId] = record;
                    // beerDocketsRedemptionsBDDict[record.beerDocketId] = record;
                }
            }

            CommonStore.update(s => {
                s.userLoyaltyStamps = userLoyaltyStamps;
                s.userLoyaltyStampsDict = userLoyaltyStampsDict;
                // s.beerDocketsRedemptionsBDDict = beerDocketsRedemptionsBDDict;
            });
        });
};

export const listenToUserAnonymousChanges = async (userIdAnonymous, outletId) => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserPointsTransactionAnonymous),
            // where('userId', '==', firebaseUid),
            where('userIdAnonymous', '==', userIdAnonymous),
            where('outletId', '==', outletId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserPointsTransactionAnonymous} changed!`);

            var anonymousPointsTransactions = [];
            var anonymousPointsBalance = 0;

            //////////////////////////////////////////////////////////////////////////////

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (!isNaN(record.amount) && typeof record.amount === 'number') {
                        anonymousPointsTransactions.push(record);
                        anonymousPointsBalance += record.amount;
                    }
                }
            }

            anonymousPointsTransactions.sort(
                (a, b) => b.createdAt - a.createdAt,
            );

            if (snapshot) {
                UserStore.update((s) => {
                    s.anonymousPointsTransactions =
                        anonymousPointsTransactions;
                    s.anonymousPointsBalance = anonymousPointsBalance;
                });
            }
        });
};

export const listenToLocationChanges = async (latParam, lngParam) => {
    // Get outlets

    // Find cities within 5km
    const center = [latParam, lngParam];
    const radiusInM = 100 * 1000; // 100km for testing

    // Each item in 'bounds' represents a startAt/endAt pair. We have to issue
    // a separate query for each pair. There can be up to 9 pairs of bounds
    // depending on overlap, but in most cases there are 4.
    const bounds = geofire.geohashQueryBounds(center, radiusInM);
    const promises = [];
    for (const b of bounds) {
        // const q = firebase.firestore().collection(Collections.Outlet)
        //     .orderBy('geoHash')
        //     .startAt(b[0])
        //     .endAt(b[1]);

        const q = query(
            collection(global.db, Collections.Outlet),
            orderBy('geoHash'),
            startAt(b[0]),
            endAt(b[1]),
        );

        // promises.push(q.get());
        promises.push(getDocs(q));
    }

    // Collect all the query results together into a single list
    Promise.all(promises).then((snapshots) => {
        const matchingDocs = [];

        for (const snap of snapshots) {
            for (const doc of snap.docs) {
                const lat = doc.get('lat');
                const lng = doc.get('lng');

                // We have to filter out a few false positives due to GeoHash
                // accuracy, but most will match
                const distanceinKM = geofire.distanceBetween([lat, lng], center);
                const distanceInM = distanceinKM * 1000;
                if (distanceInM <= radiusInM) {
                    matchingDocs.push({
                        ...doc.data(),
                        // deliveryFees: calculateDeliveryFees(doc.data()),
                        distanceInKM: distanceinKM.toFixed(2),
                    });
                }
            }
        }

        return matchingDocs;
    }).then(async (outlets) => {
        var nearbyOutlets = [];
        var outletsOpeningDict = {};
        var merchantsDict = {};
        var outletsMinMaxDict = {};

        var nearbyPromotions = [];
        var nearbyLoyaltyStamps = [];

        CommonStore.update(s => {
            outletsOpeningDict = {
                ...s.outletsOpeningDict,
            };

            merchantsDict = {
                ...s.merchantsDict,
            };

            outletsMinMaxDict = {
                ...s.outletsMinMaxDict,
            };
        });

        for (var i = 0; i < outlets.length; i++) {
            const outlet = outlets[i];

            nearbyOutlets.push(outlet);

            if (outletsOpeningDict[outlet.uniqueId] === undefined) {
                // const outletOpeningSnapshot = await firebase.firestore().collection(Collections.OutletOpening)
                //     .where('outletId', '==', outlet.uniqueId)
                //     .limit(1)
                //     .get();

                const outletOpeningSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.OutletOpening),
                        where('outletId', '==', outlet.uniqueId),
                        limit(1),
                    )
                );

                if (!outletOpeningSnapshot.empty) {
                    const outletOpening = outletOpeningSnapshot.docs[0].data();

                    outletsOpeningDict[outletOpening.outletId] = outletOpening;
                }
            }

            if (merchantsDict[outlet.merchantId] === undefined) {
                // const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
                //     .where('uniqueId', '==', outlet.merchantId)
                //     .limit(1)
                //     .get();

                const merchantSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.Merchant),
                        where('uniqueId', '==', outlet.merchantId),
                        limit(1),
                    )
                );

                if (!merchantSnapshot.empty) {
                    const merchant = merchantSnapshot.docs[0].data();

                    merchantsDict[merchant.uniqueId] = merchant;
                }
            }

            if (outletsMinMaxDict[outlet.uniqueId] === undefined) {
                outletsMinMaxDict[outlet.uniqueId] = {
                    min: null,
                    max: null,
                };

                // const outletMinSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                //     .where('outletId', '==', outlet.uniqueId)
                //     .orderBy('price', 'asc')
                //     .limit(1)
                //     .get();

                const outletMinSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.OutletItem),
                        where('outletId', '==', outlet.uniqueId),
                        orderBy('price', 'asc'),
                        limit(1),
                    )
                );

                // const outletMaxSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                //     .where('outletId', '==', outlet.uniqueId)
                //     .orderBy('price', 'desc')
                //     .limit(1)
                //     .get();

                const outletMaxSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.OutletItem),
                        where('outletId', '==', outlet.uniqueId),
                        orderBy('price', 'desc'),
                        limit(1),
                    )
                );

                if (!outletMinSnapshot.empty) {
                    const outletMin = outletMinSnapshot.docs[0].data();

                    outletsMinMaxDict[outlet.uniqueId].min = outletMin;
                }

                if (!outletMaxSnapshot.empty) {
                    const outletMax = outletMaxSnapshot.docs[0].data();

                    outletsMinMaxDict[outlet.uniqueId].max = outletMax;
                }
            }
        }

        console.log('before promotionSnapshot');

        var outletIdList = outlets.map(item => item.uniqueId).slice(0, 10);

        // const promotionSnapshot = await firebase.firestore().collection(Collections.Promotion)
        //     .where('outletId', 'in', outletIdList)
        //     // .where('notification.isLocationBasedPush', '==', true)
        //     // .limit(1)
        //     .get();

        const promotionSnapshot = await getDocs(
            query(
                collection(global.db, Collections.Promotion),
                where('outletId', 'in', outletIdList),
            )
        );

        if (!promotionSnapshot.empty) {
            promotionSnapshot.forEach(doc => {
                nearbyPromotions.push(doc.data());
            });
        }

        // await displayNearbyPromotionNotification(nearbyPromotions.filter(promotion => promotion.notification && promotion.notification.isLocationBasedPush));

        // const loyaltyStampSnapshot = await firebase.firestore().collection(Collections.LoyaltyStamp)
        //     .where('outletId', 'in', outletIdList)
        //     // .where('notification.isLocationBasedPush', '==', true)
        //     // .limit(1)
        //     .get();

        const loyaltyStampSnapshot = await getDocs(
            query(
                collection(global.db, Collections.LoyaltyStamp),
                where('outletId', 'in', outletIdList),
            )
        );

        if (!loyaltyStampSnapshot.empty) {
            loyaltyStampSnapshot.forEach(doc => {
                nearbyLoyaltyStamps.push(doc.data());
            });
        }

        CommonStore.update(s => {
            s.nearbyOutlets = nearbyOutlets;
            s.nearbyPromotions = nearbyPromotions;
            s.nearbyLoyaltyStamps = nearbyLoyaltyStamps;
            s.outletsOpeningDict = {
                // ...s.outletsOpeningDict,
                ...outletsOpeningDict,
            };

            s.searchOutlets = nearbyOutlets;

            s.merchantsDict = {
                // ...s.merchantsDict,
                ...merchantsDict,
            };

            s.outletsMinMaxDict = {
                // ...s.outletsMinMaxDict,
                ...outletsMinMaxDict,
            };
        });
    });
};

export const listenToSelectedOutletChangesCore = (outlet, email) => {
    // experimental: change to snapshot based

    CommonStore.update(s => {
        s.isSwitchingOutlets = true;
    });

    var totalOperations = 2;

    var subscriberOutletItem = () => { };
    var subscriberOutletItemCategory = () => { };

    // subscriberOutletItem = firebase.firestore()
    //     .collection(Collections.OutletItem)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .onSnapshot(snapshot => {
    subscriberOutletItem = onSnapshot(
        query(
            collection(global.db, Collections.OutletItem),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletItem} changed!`);

            if (snapshot) {
                var selectedOutletItems = [];
                var selectedOutletItemsDict = {};
                var selectedOutletItemsSkuDict = {};

                if (!snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = snapshot.docs[i].data();

                        selectedOutletItems.push(record);

                        // selectedOutletItemsDict[record.uniqueId] = record;
                        // selectedOutletItemsSkuDict[record.sku] = record;
                    }
                }

                selectedOutletItems.sort((a, b) => a.name.localeCompare(b.name));

                CommonStore.update(s => {
                    s.selectedOutletItems = selectedOutletItems;
                    // s.selectedOutletItemsDict = selectedOutletItemsDict;
                    // s.selectedOutletItemsSkuDict = selectedOutletItemsSkuDict;
                });

                totalOperations--;
                if (totalOperations <= 0) {
                    CommonStore.update(s => {
                        s.isSwitchingOutlets = false;
                    });
                }
                else {
                    // 2024-04-04 - in case the totalOperations not able to be deducted fully

                    setTimeout(() => {
                        CommonStore.update(s => {
                            s.isSwitchingOutlets = false;
                        });
                    }, 3000);
                }
            }
        });

    // subscriberOutletItemCategory = firebase.firestore()
    //     .collection(Collections.OutletItemCategory)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .onSnapshot(snapshot => {
    subscriberOutletItemCategory = onSnapshot(
        query(
            collection(global.db, Collections.OutletItemCategory),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletItemCategory} changed!`);

            if (snapshot) {
                var selectedOutletItemCategories = [];
                var selectedOutletItemCategory = {};

                var selectedOutletItemCategoriesDict = {};

                if (!snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = snapshot.docs[i].data();

                        selectedOutletItemCategories.push(record);

                        selectedOutletItemCategoriesDict[record.uniqueId] = record;
                    }

                    // selectedOutletItemCategory = selectedOutletItemCategories[0];
                }

                selectedOutletItemCategories.sort((a, b) => {
                    return naturalCompare(a.name || '', b.name || '');
                });

                // selectedOutletItemCategories.sort((a, b) => a.name.localeCompare(b.name));

                CommonStore.update(s => {
                    s.selectedOutletItemCategories = selectedOutletItemCategories;
                    // s.selectedOutletItemCategory = selectedOutletItemCategory;

                    s.selectedOutletItemCategoriesDict = selectedOutletItemCategoriesDict;
                });

                totalOperations--;
                if (totalOperations <= 0) {
                    CommonStore.update(s => {
                        s.isSwitchingOutlets = false;
                    });
                }
                else {
                    // 2024-04-04 - in case the totalOperations not able to be deducted fully

                    setTimeout(() => {
                        CommonStore.update(s => {
                            s.isSwitchingOutlets = false;
                        });
                    }, 3000);
                }
            }
        });

    //////////////////////////////////////////

    // 2024-02-27 - to record anonymous id created date

    setTimeout(async () => {
        const anonymousDtRaw = await AsyncStorage.getItem(`anonymousDt.${outlet.uniqueId}`);

        if (anonymousDtRaw) {
            // means existed

            UserStore.update(s => {
                s.anonymousDt = parseInt(anonymousDtRaw);
            });
        }
        else {
            var anonymousDtTemp = Date.now();
            UserStore.update(s => {
                s.anonymousDt = anonymousDtTemp;
            });

            await AsyncStorage.setItem(`anonymousDt.${outlet.uniqueId}`, anonymousDtTemp.toString());
        }
    }, 100);

    //////////////////////////////////////////

    return () => {
        console.log('unsubscribe from listenToSelectedOutletChangesCore');

        subscriberOutletItem();
        subscriberOutletItemCategory();
    };
};

export const listenToSelectedOutletChanges = (outlet, email, userPhone) => {
    // experimental: change to snapshot based

    var selectedOutletItems = [];
    var selectedOutletItemCategories = [];
    var selectedOutletItemCategory = {};

    var selectedOutletPromotions = [];

    var totalOperations = 2;

    var subscriberOutletItemAddOn = () => { };
    var subscriberOutletItemAddOnChoice = () => { };
    var subscriberReservationConfig = () => { };
    var subscriberQueueConfig = () => { };
    var subscriberPromotion = () => { };
    var subscriberUpsellingCampaign = () => { };
    var subscriberOutletOpening = () => { };
    // var subscriberOutletShift = () => { };
    var subscriberCRMUserTag = () => { };
    var subscriberCRMSegment = () => { };
    var subscriberLoyaltyStamp = () => { };
    var subscriberLoyaltyCampaign = () => { };
    var subscriberLoyaltyCampaignCreditTransaction = () => { };
    var subscriberUserLoyaltyCampaign = () => { };
    var subscriberTaggableVoucher = () => { };
    var subscriberUserTaggableVoucher = () => { };
    var subscriberOutletShift = () => { };
    var subscriberOutletSection = () => { };
    var subscriberOutletMerchant = () => { };
    var subscriberTopupCreditType = () => { };
    var subscriberOutletQueueNumber = () => { };

    // subscriberOutletItemAddOn = firebase.firestore()
    //     .collection(Collections.OutletItemAddOn)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .onSnapshot((snapshot) => {
    subscriberOutletItemAddOn = onSnapshot(
        query(
            collection(global.db, Collections.OutletItemAddOn),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            // console.log(`${Collections.OutletItemAddOn} changed!`);

            var outletsItemAddOn = [];
            var outletsItemAddOnDict = {};
            var outletsItemAddOnIdDict = {};

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    // outletsItemAddOn.push(record);

                    if (record.hqr) {
                        continue;
                    }

                    if (record.isShared) {
                        let outletItemIdList = record.outletItemIdList;

                        if (outletItemIdList && outletItemIdList.length > 0) {
                            outletItemIdList.forEach(item => {
                                const sharedVariantAddOn = {
                                    ...record,
                                    orderIndex: (record.orderIndexList && record.orderIndexList[item]) ?? null,
                                    printerArea: (record.pal && record.pal[item]) ?? null,
                                }

                                if (outletsItemAddOnDict[item]) {
                                    outletsItemAddOnDict[item].push(sharedVariantAddOn);
                                } else {
                                    outletsItemAddOnDict[item] = [sharedVariantAddOn];
                                }
                            })
                        }
                    }
                    else {
                        if (outletsItemAddOnDict[record.outletItemId]) {
                            outletsItemAddOnDict[record.outletItemId].push(record);
                        } else {
                            outletsItemAddOnDict[record.outletItemId] = [record];
                        }

                        outletsItemAddOnDict[record.outletItemId] =
                            outletsItemAddOnDict[record.outletItemId].sort((a, b) =>
                                a.maxSelect && a.minSelect ? -1 : 1,
                            );
                    }

                    outletsItemAddOnIdDict[record.uniqueId] = record;
                }
            }

            CommonStore.update((s) => {
                // s.outletsItemAddOn = outletsItemAddOn;
                s.outletsItemAddOnDict = outletsItemAddOnDict;
                s.outletsItemAddOnIdDict = outletsItemAddOnIdDict;
            });
        });

    // subscriberOutletItemAddOnChoice = firebase.firestore()
    //     .collection(Collections.OutletItemAddOnChoice)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .onSnapshot((snapshot) => {

    // 2023-06-17 - Hide first
    // subscriberOutletItemAddOnChoice = onSnapshot(
    //     query(
    //         collection(global.db, Collections.OutletItemAddOnChoice),
    //         where('outletId', '==', outlet.uniqueId),
    //     ),
    //     async (snapshot) => {
    //         // console.log(`${Collections.OutletItemAddOnChoice} changed!`);

    //         var outletsItemAddOnChoiceDict = {};
    //         var outletsItemAddOnChoiceIdDict = {};

    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 if (outletsItemAddOnChoiceDict[record.outletItemAddOnId]) {
    //                     outletsItemAddOnChoiceDict[record.outletItemAddOnId].push(
    //                         record,
    //                     );
    //                 } else {
    //                     outletsItemAddOnChoiceDict[record.outletItemAddOnId] = [record];
    //                 }

    //                 outletsItemAddOnChoiceIdDict[record.uniqueId] = record;
    //             }
    //         }

    //         CommonStore.update((s) => {
    //             s.outletsItemAddOnChoiceDict = outletsItemAddOnChoiceDict;
    //             s.outletsItemAddOnChoiceIdDict = outletsItemAddOnChoiceIdDict;
    //         });
    //     });        

    // subscriberPromotion = firebase.firestore()
    //     .collection(Collections.Promotion)
    //     .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {

    subscriberReservationConfig = onSnapshot(
        query(
            collection(global.db, Collections.ReservationConfig),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.ReservationConfig} changed!`);

            var reservationConfig = {};

            if (snapshot && !snapshot.empty) {
                reservationConfig = snapshot.docs[0].data();
            }

            if (snapshot) {
                CommonStore.update((s) => {
                    s.reservationConfig = reservationConfig;
                });
            }
        });

    subscriberQueueConfig = onSnapshot(
        query(
            collection(global.db, Collections.QueueConfig),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.QueueConfig} changed!`);

            let queueConfig = null;

            if (!snapshot.empty) {
                queueConfig = snapshot.docs[0].data();
            }

            CommonStore.update((s) => {
                s.queueConfig = queueConfig;
            });
        });

    subscriberPromotion = onSnapshot(
        query(
            collection(global.db, Collections.Promotion),
            where('outletIdList', 'array-contains', outlet.uniqueId),
            where('isActive', '==', true),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.Promotion} changed!`);

            var selectedOutletPromotions = [];
            var selectedOutletPromotionsDict = {};

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletPromotions.push(record);
                    selectedOutletPromotionsDict[record.uniqueId] = record;
                }
            }

            CommonStore.update(s => {
                s.selectedOutletPromotions = selectedOutletPromotions;
                s.selectedOutletPromotionsDict = selectedOutletPromotionsDict;
            });

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    // subscriberUpsellingCampaign = firebase.firestore()
    //     .collection(Collections.UpsellingCampaign)
    //     .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    subscriberUpsellingCampaign = onSnapshot(
        query(
            collection(global.db, Collections.UpsellingCampaign),
            where('outletIdList', 'array-contains', outlet.uniqueId),
            where('isActive', '==', true),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.UpsellingCampaign} changed!`);

            var selectedOutletUpsellingCampaigns = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletUpsellingCampaigns.push(record);
                }
            }

            CommonStore.update(s => {
                s.selectedOutletUpsellingCampaigns = selectedOutletUpsellingCampaigns;
            });

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    // subscriberOutletOpening = firebase.firestore()
    //     .collection(Collections.OutletOpening)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .onSnapshot(snapshot => {
    subscriberOutletOpening = onSnapshot(
        query(
            collection(global.db, Collections.OutletOpening),
            where('outletId', '==', outlet.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletOpening} changed!`);

            if (!snapshot.empty) {
                var outletsOpeningDict = {};

                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    outletsOpeningDict[record.outletId] = record;
                }

                CommonStore.update(s => {
                    s.outletsOpeningDict = outletsOpeningDict;
                });
            }

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    // subscriberOutletShift = onSnapshot(
    //     query(
    //         collection(global.db, Collections.OutletOpening),
    //         where('outletId', '==', outlet.uniqueId),
    //         orderBy('createdAt', 'desc'),
    //         limit(1),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.OutletOpening} changed!`);

    //         var currOutletShift = {};
    //         var currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;

    //         if (snapshot && !snapshot.empty) {
    //             currOutletShift = snapshot.docs[0].data();

    //             if (currOutletShift.closeDate === null) {
    //                 currOutletShiftStatus = OUTLET_SHIFT_STATUS.OPENED;
    //             } else {
    //                 currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;
    //             }
    //         }

    //         if (snapshot) {
    //             CommonStore.update((s) => {
    //                 s.currOutletShift = currOutletShift;
    //                 s.currOutletShiftStatus = currOutletShiftStatus;
    //             });
    //         }
    //     });

    // firebase.firestore()
    //     .collection(Collections.PreorderPackage)
    //     .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     // .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    //         console.log(`${Collections.PreorderPackage} changed!`);

    //         var selectedOutletPreorderPackages = [];

    //         if (!snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 selectedOutletPreorderPackages.push(record);
    //             }
    //         }

    //         CommonStore.update(s => {
    //             s.selectedOutletPreorderPackages = selectedOutletPreorderPackages;
    //         });

    //         totalOperations--;
    //         if (totalOperations <= 0) {
    //             CommonStore.update(s => {
    //                 s.isSwitchingOutlets = false;
    //             });
    //         }
    //     });

    // 2022-08-02 - Commented, not needed anymore
    // firebase.firestore()
    //     .collection(Collections.PointsRedeemPackage)
    //     .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     // .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    //         console.log(`${Collections.PointsRedeemPackage} changed!`);

    //         var selectedOutletPointsRedeemPackages = [];

    //         if (!snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 selectedOutletPointsRedeemPackages.push(record);
    //             }
    //         }

    //         CommonStore.update(s => {
    //             s.selectedOutletPointsRedeemPackages = selectedOutletPointsRedeemPackages;
    //         });

    //         totalOperations--;
    //         if (totalOperations <= 0) {
    //             CommonStore.update(s => {
    //                 s.isSwitchingOutlets = false;
    //             });
    //         }
    //     });

    // subscriberCRMUserTag = firebase.firestore()
    //     .collection(Collections.CRMUserTag)
    //     .where('merchantId', '==', outlet.merchantId)
    //     // .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {

    subscriberCRMUserTag = onSnapshot(
        query(
            collection(global.db, Collections.CRMUserTag),
            where('merchantId', '==', outlet.merchantId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.CRMUserTag} changed!`);

            var selectedOutletCRMTagsDict = {};

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletCRMTagsDict[record.uniqueId] = record;
                }
            }

            CommonStore.update(s => {
                s.selectedOutletCRMTagsDict = selectedOutletCRMTagsDict;
            });

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    subscriberCRMSegment = onSnapshot(
        query(
            collection(global.db, Collections.CRMSegment),
            where('merchantId', '==', outlet.merchantId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.CRMSegment} changed!`);

            var selectedOutletCRMSegmentsDict = {};

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletCRMSegmentsDict[record.uniqueId] = record;
                }
            }

            CommonStore.update(s => {
                s.selectedOutletCRMSegmentsDict = selectedOutletCRMSegmentsDict;
            });

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    // firebase.firestore()
    //     .collection(Collections.CRMUser)
    //     .where('merchantId', '==', outlet.merchantId)
    //     .where('email', '==', email)
    //     .where('deletedAt', '==', null)
    //     .limit(1)
    //     .onSnapshot(snapshot => {
    //         console.log(`${Collections.CRMUser} changed!`);

    //         var selectedOutletCRMUser = {};

    //         if (!snapshot.empty) {
    //             selectedOutletCRMUser = snapshot.docs[0].data();
    //         }

    //         CommonStore.update(s => {
    //             s.selectedOutletCRMUser = selectedOutletCRMUser;
    //         });

    //         totalOperations--;
    //         if (totalOperations <= 0) {
    //             CommonStore.update(s => {
    //                 s.isSwitchingOutlets = false;
    //             });
    //         }
    //     });

    // firebase.firestore()
    //     .collection(Collections.OutletReviewSummary)
    //     .where('merchantId', '==', outlet.merchantId)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .where('deletedAt', '==', null)
    //     .limit(1)
    //     .onSnapshot(snapshot => {
    //         console.log(`${Collections.OutletReviewSummary} changed!`);

    //         var selectedOutletReviewSummary = {};

    //         if (!snapshot.empty) {
    //             selectedOutletReviewSummary = snapshot.docs[0].data();
    //         }
    //         else {
    //             selectedOutletReviewSummary = {
    //                 ratingsList: [],
    //                 ratingsCount: 0,
    //                 ratingsAverage: 0,
    //                 commentsCount: 0,
    //             };
    //         }

    //         CommonStore.update(s => {
    //             s.selectedOutletReviewSummary = selectedOutletReviewSummary;
    //         });

    //         totalOperations--;
    //         if (totalOperations <= 0) {
    //             CommonStore.update(s => {
    //                 s.isSwitchingOutlets = false;
    //             });
    //         }
    //     });

    // subscriberLoyaltyStamp = firebase.firestore()
    //     .collection(Collections.LoyaltyStamp)
    //     .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     // .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    subscriberLoyaltyStamp = onSnapshot(
        query(
            collection(global.db, Collections.LoyaltyStamp),
            // where('outletIdList', 'array-contains', outlet.uniqueId),
            where('outletId', '==', outlet.uniqueId),
            // .where('isActive', '==', true)
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.LoyaltyStamp} changed!`);

            var selectedOutletLoyaltyStamps = [];
            var selectedOutletLoyaltyStampsDict = {};

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletLoyaltyStamps.push(record);
                    selectedOutletLoyaltyStampsDict[record.uniqueId] = record;
                }
            }

            CommonStore.update(s => {
                s.selectedOutletLoyaltyStamps = selectedOutletLoyaltyStamps;
                s.selectedOutletLoyaltyStampsDict = selectedOutletLoyaltyStampsDict;
            });
        });

    // subscriberLoyaltyCampaign = firebase.firestore()
    //     .collection(Collections.LoyaltyCampaign)
    //     // .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .where('isActive', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    subscriberLoyaltyCampaign = onSnapshot(
        query(
            collection(global.db, Collections.LoyaltyCampaign),
            where('outletId', '==', outlet.uniqueId),
            where('isActive', '==', true),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.LoyaltyCampaign} changed!`);

            var selectedOutletLoyaltyCampaigns = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletLoyaltyCampaigns.push(record);
                }
            }

            CommonStore.update(s => {
                s.selectedOutletLoyaltyCampaigns = selectedOutletLoyaltyCampaigns;
            });

            // totalOperations--;
            // if (totalOperations <= 0) {
            //     CommonStore.update(s => {
            //         s.isSwitchingOutlets = false;
            //     });
            // }
        });

    // subscriberLoyaltyCampaignCreditTransaction = firebase.firestore()
    //     .collection(Collections.LoyaltyCampaignCreditTransaction)
    //     .where('email', '==', email)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    subscriberLoyaltyCampaignCreditTransaction = onSnapshot(
        query(
            collection(global.db, Collections.LoyaltyCampaignCreditTransaction),
            where('email', '==', email),
            where('outletId', '==', outlet.uniqueId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.LoyaltyCampaignCreditTransaction} changed!`);

            var selectedOutletLCCTransactions = [];
            var selectedOutletLCCBalance = 0;

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (!isNaN(record.amount) && typeof record.amount === 'number') {
                        selectedOutletLCCTransactions.push(record);
                        selectedOutletLCCBalance += record.amount;
                    }
                }
            }

            selectedOutletLCCTransactions.sort((a, b) => b.createdAt - a.createdAt);

            CommonStore.update(s => {
                s.selectedOutletLCCTransactions = selectedOutletLCCTransactions;
                s.selectedOutletLCCBalance = selectedOutletLCCBalance;
            });
        });

    // subscriberUserLoyaltyCampaign = firebase.firestore()
    //     .collection(Collections.UserLoyaltyCampaign)
    //     // .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     .where('outletId', '==', outlet.uniqueId)
    //     .where('email', '==', email)
    //     // .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    // subscriberUserLoyaltyCampaign = onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserLoyaltyCampaign),
    //         where('outletId', '==', outlet.uniqueId),
    //         where('email', '==', email),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserLoyaltyCampaign} changed!`);

    //         var selectedOutletUserLoyaltyCampaigns = [];

    //         if (!snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 selectedOutletUserLoyaltyCampaigns.push(record);
    //             }
    //         }

    //         CommonStore.update(s => {
    //             s.selectedOutletUserLoyaltyCampaigns = selectedOutletUserLoyaltyCampaigns;
    //         });

    //         // totalOperations--;
    //         // if (totalOperations <= 0) {
    //         //     CommonStore.update(s => {
    //         //         s.isSwitchingOutlets = false;
    //         //     });
    //         // }
    //     });

    // subscriberTaggableVoucher = firebase.firestore()
    //     .collection(Collections.TaggableVoucher)
    //     // .where('outletIdList', 'array-contains', outlet.uniqueId)
    //     .where('outletId', '==', outlet.uniqueId)
    //     // .where('isBuyableOnline', '==', true)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {

    subscriberTaggableVoucher = onSnapshot(
        query(
            collection(global.db, Collections.TaggableVoucher),
            where('merchantId', '==', outlet.merchantId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.TaggableVoucher} changed!`);

            if (snapshot && !snapshot.empty) {
                const now = moment();
                const records = snapshot.docs.map(doc => doc.data());

                const selectedOutletTaggableVouchers = [];
                const selectedOutletTaggableVouchersAll = [];
                const tempVoucherList = [];

                records.forEach(record => {
                    const endDate = moment(record.promoDateEnd).set({
                        hour: moment(record.promoTimeEnd).get('hour'),
                        minute: moment(record.promoTimeEnd).get('minute'),
                    });
                    const startDate = moment(record.promoDateStart).set({
                        hour: moment(record.promoTimeStart).get('hour'),
                        minute: moment(record.promoTimeStart).get('minute'),
                    });

                    if (now.isBefore(endDate) && now.isSameOrAfter(startDate)) {
                        if (record.isBuyableOnline) {
                            selectedOutletTaggableVouchers.push(record);
                        }
                        selectedOutletTaggableVouchersAll.push(record);

                        if (
                            ((
                                record.voucherType === LOYALTY_PROMOTION_TYPE.TAKE_PERCENTAGE_OFF ||
                                record.voucherType === LOYALTY_PROMOTION_TYPE.TAKE_AMOUNT_OFF ||
                                record.voucherType === LOYALTY_PROMOTION_TYPE.FREE_ITEM ||
                                record.voucherType === LOYALTY_PROMOTION_TYPE.ZUS_BUNDLE)) &&
                            record.outletIdList.includes(outlet.uniqueId)
                        ) {
                            tempVoucherList.push(record);
                        }
                    }
                });

                CommonStore.update(s => {
                    s.selectedOutletTaggableVouchers = selectedOutletTaggableVouchers;
                    s.selectedOutletTaggableVouchersAll = selectedOutletTaggableVouchersAll;
                });

                TempStore.update(s => {
                    s.claimVoucherList = tempVoucherList;
                });
            }
        }

    );

    subscriberUserTaggableVoucher = onSnapshot(
        query(
            collection(global.db, Collections.UserTaggableVoucher),
            where('merchantId', '==', outlet.merchantId),
            where('phone', '==', userPhone),
            where('redeemDate', '==', null),
            where('expirationDate', '>', moment().valueOf()),
            orderBy('expirationDate'),
            limit(50),
        ),

        async (snapshot) => {
            if (userPhone === "") return;
            console.log(`${Collections.UserTaggableVoucher} changed!`);

            const userTaggableVouchers = snapshot?.docs?.map(doc => doc.data()) || [];

            CommonStore.update((s) => {
                s.userTaggableVouchers = userTaggableVouchers;
            });
        }
    );

    subscriberOutletShift = onSnapshot(
        query(
            collection(global.db, Collections.OutletShift),
            where('outletId', '==', outlet.uniqueId),
            orderBy('createdAt', 'desc'),
            limit(1),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletShift} changed!`);

            var currOutletShift = {};
            var currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;

            if (snapshot && !snapshot.empty) {
                currOutletShift = snapshot.docs[0].data();

                if (currOutletShift.closeDate === null) {
                    currOutletShiftStatus = OUTLET_SHIFT_STATUS.OPENED;
                } else {
                    currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;
                }
            }

            if (snapshot) {
                CommonStore.update((s) => {
                    s.currOutletShift = currOutletShift;
                    s.currOutletShiftStatus = currOutletShiftStatus;
                });
            }
        });

    subscriberOutletSection = onSnapshot(
        query(
            collection(global.db, Collections.OutletSection),
            where('outletId', '==', outlet.uniqueId),
            // orderBy('createdAt', 'desc'),
            // limit(1),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletSection} changed!`);

            var selectedOutletSections = [];

            if (snapshot && !snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    selectedOutletSections.push(record);
                }
            }

            CommonStore.update((s) => {
                s.selectedOutletSections =
                    selectedOutletSections;
            });
        });

    subscriberOutletMerchant = onSnapshot(
        query(
            collection(global.db, Collections.Merchant),
            where('uniqueId', '==', outlet.merchantId),
            // orderBy('createdAt', 'desc'),
            limit(1),
        ),
        async (snapshot) => {
            console.log(`${Collections.Merchant} changed!`);

            var selectedMerchant = {};

            if (snapshot && !snapshot.empty) {
                selectedMerchant = snapshot.docs[0].data();
            }

            if (snapshot) {
                CommonStore.update((s) => {
                    s.selectedMerchant = selectedMerchant;
                });
            }
        });

    subscriberTopupCreditType = onSnapshot(
        query(
            collection(global.db, Collections.TopupCreditType),
            where('merchantId', '==', outlet.merchantId),
            where('deletedAt', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.TopupCreditType} changed!`);

            if (snapshot && !snapshot.empty) {
                var topupCreditTypes = [];

                if (snapshot && !snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = snapshot.docs[i].data();

                        topupCreditTypes.push(record);
                    }
                }

                if (snapshot) {
                    CommonStore.update((s) => {
                        s.topupCreditTypes = topupCreditTypes;
                    });
                }
            }
        }

    );

    subscriberOutletQueueNumber = onSnapshot(
        query(
            collection(global.db, Collections.UserQueue),
            where('outletId', '==', outlet.uniqueId),
            where('status', 'in', [USER_QUEUE_STATUS.PENDING, USER_QUEUE_STATUS.ACCEPTED]),
            where('deletedAt', '==', null),
            orderBy('createdAt', 'asc'),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserQueue} changed!`);

            const queueData = snapshot.docs.map(doc => doc.data());

            const queueNumbers = queueData.reduce((acc, queue) => {
                if (!acc[queue.category]) {
                    acc[queue.category] = [];
                }
                acc[queue.category].push(queue.uniqueId);
                return acc;
            }, {});

            CommonStore.update((s) => {
                s.outletQueueNumberDict = queueNumbers;
            });
        }
    );

    return () => {
        subscriberOutletItemAddOn();
        subscriberOutletItemAddOnChoice();
        subscriberReservationConfig();
        subscriberPromotion();
        subscriberUpsellingCampaign();
        subscriberOutletOpening();
        // subscriberOutletShift();
        subscriberCRMUserTag();
        subscriberCRMSegment();
        subscriberLoyaltyStamp();
        subscriberLoyaltyCampaign();
        subscriberLoyaltyCampaignCreditTransaction();
        subscriberUserLoyaltyCampaign();
        subscriberTaggableVoucher();
        subscriberUserTaggableVoucher();
        subscriberOutletShift();
        subscriberOutletSection();
        subscriberOutletMerchant();
        subscriberTopupCreditType();
        subscriberOutletQueueNumber();
    };
};

export const listenToSelectedOutletItemChanges = async (item) => {
    const [outletItemAddOnChoiceSnapshot, outletItemSharedAddOnChoiceSnapshot] = await Promise.all([
        getDocs(
            query(
                collection(global.db, Collections.OutletItemAddOnChoice),
                where('outletItemId', '==', item.uniqueId)
            )
        ),
        getDocs(
            query(
                collection(global.db, Collections.OutletItemAddOnChoice),
                where('outletItemIdList', 'array-contains', item.uniqueId)
            )
        )
    ]);
    var subscriberOutletItemAddOnChoice = () => { };
    var outletsItemAddOnChoiceDict = {};
    var outletsItemAddOnChoiceIdDict = {};

    const processSnapshot = (snapshot) => {
        snapshot.forEach((doc) => {
            const record = doc.data();

            if (outletsItemAddOnChoiceDict[record.outletItemAddOnId]) {
                outletsItemAddOnChoiceDict[record.outletItemAddOnId].push(record);
            } else {
                outletsItemAddOnChoiceDict[record.outletItemAddOnId] = [record];
            }

            outletsItemAddOnChoiceIdDict[record.uniqueId] = record;
        });
    };

    await Promise.all([
        processSnapshot(outletItemAddOnChoiceSnapshot),
        processSnapshot(outletItemSharedAddOnChoiceSnapshot)
    ]);

    Object.keys(outletsItemAddOnChoiceDict).forEach(key => {
        outletsItemAddOnChoiceDict[key].sort((a, b) => {
            const aOrderIndex = a.orderIndex !== undefined ? a.orderIndex : Infinity;
            const bOrderIndex = b.orderIndex !== undefined ? b.orderIndex : Infinity;

            return aOrderIndex - bOrderIndex;
        });
    });

    const updatedOutletItemAddOnChoiceDict = {
        ...global.cartOutletItemAddOnChoiceDict,
        ...outletsItemAddOnChoiceDict,
    };

    const updatedOutletsItemAddOnChoiceIdDict = {
        ...global.outletsItemAddOnChoiceIdDict,
        ...outletsItemAddOnChoiceIdDict,
    };

    global.cartOutletItemAddOnChoiceDict = updatedOutletItemAddOnChoiceDict;
    global.outletsItemAddOnChoiceIdDict = updatedOutletsItemAddOnChoiceIdDict;

    CommonStore.update((s) => {
        s.outletsItemAddOnChoiceDict = updatedOutletItemAddOnChoiceDict;
        s.outletsItemAddOnChoiceIdDict = updatedOutletsItemAddOnChoiceIdDict;
    });

    return () => {
        subscriberOutletItemAddOnChoice();
    };
};

export const listenToCommonChanges = async () => {
    // firebase.firestore()
    //     .collection(Collections.Tag)
    //     .onSnapshot(snapshot => {

    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.Tag),
    //         // where('outletId', '==', outlet.uniqueId),
    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.Tag} changed!`);

    //         if (snapshot && !snapshot.empty) {
    //             var tempTags = [];
    //             var tempSelectedOutletTag = null;

    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 tempTags.push(record);
    //             }

    //             tempSelectedOutletTag = tempTags[0];

    //             CommonStore.update(s => {
    //                 s.tags = tempTags;
    //                 s.selectedOutletTag = tempSelectedOutletTag;
    //             });
    //         }
    //     });
};

export const listenToSelectedOutletTagChanges = async (tag) => {
    // firebase.firestore()
    //     .collection(Collections.OutletTag)
    //     .where('tagId', '==', tag.uniqueId)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.OutletTag),
            where('tagId', '==', tag.uniqueId),
        ),
        async (snapshot) => {
            console.log(`${Collections.OutletTag} changed!`);

            var tempTagOutlets = [];
            var outletsOpeningDict = {};
            var merchantsDict = {};
            var outletsMinMaxDict = {};

            // todo: disabled first, need check on this error: `Cannot perform 'get' on a proxy that has been revoked`
            // CommonStore.update(s => {
            //     outletsOpeningDict = {
            //         ...s.outletsOpeningDict,
            //     };

            //     merchantsDict = {
            //         ...s.merchantsDict,
            //     };

            //     outletsMinMaxDict = {
            //         ...s.outletsMinMaxDict,
            //     };
            // });

            if (!snapshot.empty) {
                var tempOutletTags = [];
                var outlets = [];

                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    tempOutletTags.push(record);

                    // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                    //     .where('uniqueId', '==', record.outletId)
                    //     .limit(1)
                    //     .get();

                    const outletSnapshot = await getDocs(
                        query(
                            collection(global.db, Collections.Outlet),
                            where('uniqueId', '==', record.outletId),
                            limit(1),
                        )
                    );

                    if (!outletSnapshot.empty) {
                        const outlet = outletSnapshot.docs[0].data();

                        outlets.push(outlet);
                    }
                }

                CommonStore.update(s => {
                    s.tagToOutletTagDict[tag.uniqueId] = tempOutletTags;
                });

                ////////////////////////////////////////////////

                // var tagOutlets = [];
                // var outletsOpeningDict = {};
                // var merchantsDict = {};
                // var outletsMinMaxDict = {};

                // CommonStore.update(s => {
                //     outletsOpeningDict = {
                //         ...s.outletsOpeningDict,
                //     };

                //     merchantsDict = {
                //         ...s.merchantsDict,
                //     };

                //     outletsMinMaxDict = {
                //         ...s.outletsMinMaxDict,
                //     };
                // });

                for (var i = 0; i < outlets.length; i++) {
                    const outlet = outlets[i];

                    tempTagOutlets.push(outlet);

                    if (outletsOpeningDict[outlet.uniqueId] === undefined) {
                        // const outletOpeningSnapshot = await firebase.firestore().collection(Collections.OutletOpening)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .limit(1)
                        //     .get();

                        const outletOpeningSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletOpening),
                                where('outletId', '==', outlet.uniqueId),
                                limit(1),
                            )
                        );

                        if (!outletOpeningSnapshot.empty) {
                            const outletOpening = outletOpeningSnapshot.docs[0].data();

                            outletsOpeningDict[outletOpening.outletId] = outletOpening;
                        }
                    }

                    if (merchantsDict[outlet.merchantId] === undefined) {
                        // const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
                        //     .where('uniqueId', '==', outlet.merchantId)
                        //     .limit(1)
                        //     .get();

                        const merchantSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.Merchant),
                                where('uniqueId', '==', outlet.merchantId),
                                limit(1),
                            )
                        );

                        if (!merchantSnapshot.empty) {
                            const merchant = merchantSnapshot.docs[0].data();

                            merchantsDict[merchant.uniqueId] = merchant;
                        }
                    }

                    if (outletsMinMaxDict[outlet.uniqueId] === undefined) {
                        outletsMinMaxDict[outlet.uniqueId] = {
                            min: null,
                            max: null,
                        };

                        // const outletMinSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'asc')
                        //     .limit(1)
                        //     .get();

                        const outletMinSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'asc'),
                                limit(1),
                            )
                        );

                        // const outletMaxSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'desc')
                        //     .limit(1)
                        //     .get();

                        const outletMaxSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'desc'),
                                limit(1),
                            )
                        );

                        if (!outletMinSnapshot.empty) {
                            const outletMin = outletMinSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].min = outletMin;
                        }

                        if (!outletMaxSnapshot.empty) {
                            const outletMax = outletMaxSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].max = outletMax;
                        }
                    }
                }
            }

            CommonStore.update(s => {
                s.tagOutlets = [
                    ...tempTagOutlets,
                ];

                s.outletsOpeningDict = {
                    // ...s.outletsOpeningDict,
                    ...outletsOpeningDict,
                };

                // s.searchOutlets = tagOutlets;

                s.merchantsDict = {
                    // ...s.merchantsDict,
                    ...merchantsDict,
                };

                s.outletsMinMaxDict = {
                    // ...s.outletsMinMaxDict,
                    ...outletsMinMaxDict,
                };
            });
        });
};

export const listenToSearchOutletMerchantIdChanges = async (searchOutletMerchantId) => {
    // Get outlets

    // firebase.firestore()
    //     .collection(Collections.Outlet)
    //     // .orderBy('name')
    //     // .startAt([searchOutletText])
    //     // .endAt([searchOutletText + '\uf8ff'])
    //     // .where('name', '>=', searchOutletText)
    //     // .where('name', '<', searchOutletText + 'z')
    //     .where('merchantId', '==', searchOutletMerchantId)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.Outlet),
            where('merchantId', '==', searchOutletMerchantId),
        ),
        async (snapshot) => {
            console.log(`${Collections.Outlet} changed!`);
            console.log(snapshot);

            if (!snapshot.empty) {
                var outlets = [];

                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    outlets.push(record);
                }

                var searchOutlets = [];
                var outletsOpeningDict = {};
                var merchantsDict = {};
                var outletsMinMaxDict = {};

                // CommonStore.update(s => {
                //     outletsOpeningDict = {
                //         ...s.outletsOpeningDict,
                //     };

                //     merchantsDict = {
                //         ...s.merchantsDict,
                //     };

                //     outletsMinMaxDict = {
                //         ...s.outletsMinMaxDict,
                //     };
                // });

                for (var i = 0; i < outlets.length; i++) {
                    const outlet = outlets[i];

                    searchOutlets.push(outlet);

                    if (outletsOpeningDict[outlet.uniqueId] === undefined) {
                        // const outletOpeningSnapshot = await firebase.firestore().collection(Collections.OutletOpening)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .limit(1)
                        //     .get();

                        const outletOpeningSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletOpening),
                                where('outletId', '==', outlet.uniqueId),
                                limit(1),
                            )
                        );

                        if (!outletOpeningSnapshot.empty) {
                            const outletOpening = outletOpeningSnapshot.docs[0].data();

                            outletsOpeningDict[outletOpening.outletId] = outletOpening;
                        }
                    }

                    if (merchantsDict[outlet.merchantId] === undefined) {
                        // const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
                        //     .where('uniqueId', '==', outlet.merchantId)
                        //     .limit(1)
                        //     .get();

                        const merchantSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.Merchant),
                                where('uniqueId', '==', outlet.merchantId),
                                limit(1),
                            )
                        );

                        if (!merchantSnapshot.empty) {
                            const merchant = merchantSnapshot.docs[0].data();

                            merchantsDict[merchant.uniqueId] = merchant;
                        }
                    }

                    if (outletsMinMaxDict[outlet.uniqueId] === undefined) {
                        outletsMinMaxDict[outlet.uniqueId] = {
                            min: null,
                            max: null,
                        };

                        // const outletMinSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'asc')
                        //     .limit(1)
                        //     .get();

                        const outletMinSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'asc'),
                                limit(1),
                            )
                        );

                        // const outletMaxSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'desc')
                        //     .limit(1)
                        //     .get();

                        const outletMaxSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'desc'),
                                limit(1),
                            )
                        );

                        if (!outletMinSnapshot.empty) {
                            const outletMin = outletMinSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].min = outletMin;
                        }

                        if (!outletMaxSnapshot.empty) {
                            const outletMax = outletMaxSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].max = outletMax;
                        }
                    }
                }

                console.log('searchOutlets');
                console.log(searchOutlets);

                CommonStore.update(s => {
                    s.searchOutlets = searchOutlets;

                    s.outletsOpeningDict = {
                        // ...s.outletsOpeningDict,
                        ...outletsOpeningDict,
                    };

                    s.merchantsDict = {
                        // ...s.merchantsDict,
                        ...merchantsDict,
                    };

                    s.outletsMinMaxDict = {
                        // ...s.outletsMinMaxDict,
                        ...outletsMinMaxDict,
                    };

                    s.isLoadingSearchOutlet = false;
                });
            }
        });
};

export const listenToSearchOutletTextChanges = async (searchOutletText) => {
    // Get outlets

    // firebase.firestore()
    //     .collection(Collections.Outlet)
    //     // .orderBy('name')
    //     // .startAt([searchOutletText])
    //     // .endAt([searchOutletText + '\uf8ff'])
    //     .where('name', '>=', searchOutletText)
    //     .where('name', '<', searchOutletText + 'z')
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.Outlet),
            where('name', '>=', searchOutletText),
            where('name', '<', searchOutletText + 'z'),
        ),
        async (snapshot) => {
            console.log(`${Collections.Outlet} changed!`);

            if (!snapshot.empty) {
                var outlets = [];

                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    outlets.push(record);
                }

                var searchOutlets = [];
                var outletsOpeningDict = {};
                var merchantsDict = {};
                var outletsMinMaxDict = {};

                // CommonStore.update(s => {
                //     outletsOpeningDict = {
                //         ...s.outletsOpeningDict,
                //     };

                //     merchantsDict = {
                //         ...s.merchantsDict,
                //     };

                //     outletsMinMaxDict = {
                //         ...s.outletsMinMaxDict,
                //     };
                // });

                for (var i = 0; i < outlets.length; i++) {
                    const outlet = outlets[i];

                    searchOutlets.push(outlet);

                    if (outletsOpeningDict[outlet.uniqueId] === undefined) {
                        // const outletOpeningSnapshot = await firebase.firestore().collection(Collections.OutletOpening)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .limit(1)
                        //     .get();

                        const outletOpeningSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletOpening),
                                where('outletId', '==', outlet.uniqueId),
                                limit(1),
                            )
                        );

                        if (!outletOpeningSnapshot.empty) {
                            const outletOpening = outletOpeningSnapshot.docs[0].data();

                            outletsOpeningDict[outletOpening.outletId] = outletOpening;
                        }
                    }

                    if (merchantsDict[outlet.merchantId] === undefined) {
                        // const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
                        //     .where('uniqueId', '==', outlet.merchantId)
                        //     .limit(1)
                        //     .get();

                        const merchantSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.Merchant),
                                where('uniqueId', '==', outlet.merchantId),
                                limit(1),
                            )
                        );

                        if (!merchantSnapshot.empty) {
                            const merchant = merchantSnapshot.docs[0].data();

                            merchantsDict[merchant.uniqueId] = merchant;
                        }
                    }

                    if (outletsMinMaxDict[outlet.uniqueId] === undefined) {
                        outletsMinMaxDict[outlet.uniqueId] = {
                            min: null,
                            max: null,
                        };

                        // const outletMinSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'asc')
                        //     .limit(1)
                        //     .get();

                        const outletMinSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'asc'),
                                limit(1),
                            )
                        );

                        // const outletMaxSnapshot = await firebase.firestore().collection(Collections.OutletItem)
                        //     .where('outletId', '==', outlet.uniqueId)
                        //     .orderBy('price', 'desc')
                        //     .limit(1)
                        //     .get();

                        const outletMaxSnapshot = await getDocs(
                            query(
                                collection(global.db, Collections.OutletItem),
                                where('outletId', '==', outlet.uniqueId),
                                orderBy('price', 'desc'),
                                limit(1),
                            )
                        );

                        if (!outletMinSnapshot.empty) {
                            const outletMin = outletMinSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].min = outletMin;
                        }

                        if (!outletMaxSnapshot.empty) {
                            const outletMax = outletMaxSnapshot.docs[0].data();

                            outletsMinMaxDict[outlet.uniqueId].max = outletMax;
                        }
                    }
                }

                console.log('searchOutlets');
                console.log(searchOutlets);

                CommonStore.update(s => {
                    s.searchOutlets = searchOutlets;

                    s.outletsOpeningDict = {
                        // ...s.outletsOpeningDict,
                        ...outletsOpeningDict,
                    };

                    s.merchantsDict = {
                        // ...s.merchantsDict,
                        ...merchantsDict,
                    };

                    s.outletsMinMaxDict = {
                        // ...s.outletsMinMaxDict,
                        ...outletsMinMaxDict,
                    };
                });
            }
        });
};

export const getImageFromFirebaseStorage = async (imageUrl, callback) => {
    try {
        // const url = await firebase.storage()
        //     .ref(imageUrl)
        //     .getDownloadURL()
        //     .then(url => {
        //         console.log(url);

        //         // return url;
        //         callback && callback(url);
        //     });

        const url = await getDownloadURL(
            ref(global.storage, imageUrl)
        )
            .then(url => {
                console.log(url);

                // return url;
                callback && callback(url);
            });

        // const url = await storage()
        //     .ref(imageUrl)
        //     .getDownloadURL();

        // return url;
    }
    catch (ex) {
        console.error(ex);

        return '';
    }
};

export const listenToSelectedOutletTableIdChanges = async (selectedOutletTableId, toggleOpenOrder) => {
    // Get outlets

    // 2022-10-12 - Hide first
    // firebase.firestore()
    //     .collection(Collections.UserCart)
    //     .where('tableId', '==', selectedOutletTableId)
    //     .where('outletId', '==', selectedOutletId)
    //     .where('userId', '==', userId)
    //     .limit(1)
    //     .onSnapshot(async snapshot => {
    //         console.log(`${Collections.UserCart} changed!`);
    //         console.log(snapshot);

    //         var userCart = {};
    //         var cartItems = [];

    //         if (!snapshot.empty) {
    //             const record = snapshot.docs[0].data();

    //             userCart = record;

    //             cartItems = record.cartItems;
    //         }

    //         if (userCart && userCart.userId) {
    //             AsyncStorage.setItem(`${userId}.cartItems`, JSON.stringify(cartItems));
    //             AsyncStorage.setItem(`${userId}.cartOutletId`, selectedOutletId);
    //         }
    //         else {
    //             AsyncStorage.removeItem(`${userId}.cartItems`);
    //             AsyncStorage.removeItem(`${userId}.cartOutletId`);
    //         }

    //         CommonStore.update(s => {
    //             s.userCart = userCart;
    //             // s.cartItems = cartItems;
    //         });
    //     });

    console.log('selectedOutletTableId');
    console.log(selectedOutletTableId);

    // firebase.firestore()
    //     .collection(Collections.UserOrder)
    //     .where('tableId', '==', selectedOutletTableId)
    //     .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
    //     // .where('outletId', '==', selectedOutletId)
    //     // .where('userId', '==', userId)
    //     // .limit(1)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserOrder),
            where('tableId', '==', selectedOutletTableId),
            where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED),
            where(
                'createdAt',
                '>=',
                !toggleOpenOrder
                    ?
                    moment().subtract(1, 'day').startOf('day').valueOf()
                    :
                    moment().subtract(30, 'day').startOf('day').valueOf(),
            ) // to limit to recent orders only
        ),
        async (snapshot) => {
            console.log(`${Collections.UserOrder} changed!`);
            console.log(snapshot);

            var selectedTableOrders = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (
                        record.orderStatus !==
                        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                        record.orderStatus !==
                        USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                    ) {
                        selectedTableOrders.push(record);
                    }
                }
            }

            CommonStore.update(s => {
                s.selectedTableOrders = selectedTableOrders;
            });
        });
};

// 2023-01-09 - Allow to forward user to new joined table link, after joined
export const listenToJoinedOrSplittedTableChanges = async (selectedOutletTableId) => {
    // firebase.firestore()
    //     .collection(Collections.OutletTable)
    //     .where('joinedTableIdList', 'array-contains', selectedOutletTableId)
    //     // .where('outletId', '==', selectedOutletId)
    //     // .where('userId', '==', userId)
    //     // .limit(1)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.OutletTable),
            where('joinedTableIdList', 'array-contains', selectedOutletTableId),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserOrder} changed!`);
            console.log(snapshot);

            if (snapshot) {
                var selectedOutletTableIdTemp = '';
                var record = null;

                if (!snapshot.empty) {
                    record = snapshot.docs[0].data();

                    selectedOutletTableIdTemp = record.uniqueId;
                }

                if (selectedOutletTableIdTemp && record && record.uniqueId) {
                    global.isTableJoinedOnTheFly = true;

                    CommonStore.update(s => {
                        s.selectedOutletTableId = selectedOutletTableIdTemp;
                        s.selectedOutletTablePax = record.seated;
                        s.selectedOutletTableCode = record.code;

                        s.selectedOutletSectionId = record.outletSectionId;
                    });

                    if (record.seated <= 0) {
                        global.errorMsg = 'Table is already unseated (CF-01).';

                        global.linkToFunc && global.linkToFunc(`${prefix}/scan`);
                    }
                }
                else {
                    // global.isTableJoinedOnTheFly = false;
                }
            }
        });

    // firebase.firestore()
    //     .collection(Collections.OutletTable)
    //     .where('uniqueId', '==', selectedOutletTableId)
    //     // .where('outletId', '==', selectedOutletId)
    //     // .where('userId', '==', userId)
    //     // .limit(1)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.OutletTable),
            where('uniqueId', '==', selectedOutletTableId),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserOrder} changed!`);
            console.log(snapshot);

            if (snapshot) {
                var record = null;

                if (!snapshot.empty) {
                    record = snapshot.docs[0].data();
                }

                if (record && record.uniqueId) {
                    CommonStore.update(s => {
                        s.selectedOutletTablePax = record.seated;

                        s.selectedOutletSectionId = record.outletSectionId;
                    });

                    if (record.seated <= 0) {
                        global.errorMsg = 'Table is already unseated (CF-02).';

                        global.linkToFunc && global.linkToFunc(`${prefix}/scan`);
                    }
                }
                else {
                    // global.isTableJoinedOnTheFly = false;
                }
            }
        });
};

export const listenToUserIdAnonymousChanges = async (userIdAnonymous, selectedOutletId) => {
    // firebase.firestore()
    //     .collection(Collections.UserOrder)
    //     .where('userIdAnonymous', '==', userIdAnonymous)
    //     .where('outletId', '==', selectedOutletId)
    //     .where('completedDate', '==', null)
    //     // .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
    //     // .where('outletId', '==', selectedOutletId)
    //     // .where('userId', '==', userId)
    //     // .limit(1)
    //     .onSnapshot(async snapshot => {
    onSnapshot(
        query(
            collection(global.db, Collections.UserOrder),
            where('userIdAnonymous', '==', userIdAnonymous),
            where('outletId', '==', selectedOutletId),
            where('completedDate', '==', null),
        ),
        async (snapshot) => {
            console.log(`${Collections.UserOrder} changed!`);
            console.log(snapshot);

            var selectedUserOrderAnonymous = [];
            var selectedUserOrderAnonymousTakeaway = [];

            if (!snapshot.empty) {
                for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (
                        record.orderStatus !==
                        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                        record.orderStatus !==
                        USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                    ) {
                        selectedUserOrderAnonymous.push(record);

                        if (record.orderType === ORDER_TYPE.PICKUP) {
                            selectedUserOrderAnonymousTakeaway.push(record);
                        }
                    }
                }
            }

            CommonStore.update(s => {
                s.selectedUserOrderAnonymous = selectedUserOrderAnonymous;
                s.selectedUserOrderAnonymousTakeaway = selectedUserOrderAnonymousTakeaway;
            });
        });
};

export const listenToUserIdAnonymousChangesStatic = async (userIdAnonymous, selectedOutletId) => {
    onSnapshot(
        query(
            collection(global.db, Collections.OrderPaymentStatus),
            where('userIdAnonymous', '==', userIdAnonymous),
            where('outletId', '==', selectedOutletId),
            // where('completedDate', '==', null),
            limit(1),
        ),
        async (snapshot) => {
            console.log(`${Collections.OrderPaymentStatus} changed!`);
            console.log(snapshot);

            if (snapshot) {
                var orderPaymentStatus = null;

                if (!snapshot.empty) {
                    const record = snapshot.docs[0].data();

                    orderPaymentStatus = record;
                }

                CommonStore.update(s => {
                    s.orderPaymentStatus = orderPaymentStatus;
                });
            }
        });
};

export const listenToOrderQueueUserOrderChanges = async (
    currOutletId,
    toggleOpenOrder,
) => {
    try {
        var subscriberUserOrderQueue = () => { };
        var subscriberUserOrderPreparing = () => { };

        subscriberUserOrderQueue = onSnapshot(
            query(
                collection(global.db, Collections.UserOrder),
                where('outletId', '==', currOutletId),
                where('orderStatus', 'in', [
                    // USER_ORDER_STATUS.ORDER_RECEIVED,
                    // USER_ORDER_STATUS.ORDER_AUTHORIZED,
                    // USER_ORDER_STATUS.ORDER_PREPARING,
                    USER_ORDER_STATUS.ORDER_PREPARED,
                    USER_ORDER_STATUS.ORDER_DELIVERED,

                    // USER_ORDER_STATUS.ORDER_COMPLETED,

                    // USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
                    // USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                    // USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,

                    // USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                    // USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                ]),
                // where('createdAt', '>=', moment().subtract(1, 'day')
                //     .startOf('day').valueOf()) //current set to 1 day
                where(
                    'createdAt',
                    '>=',
                    !toggleOpenOrder
                        ?
                        moment().subtract(1, 'day').startOf('day').valueOf()
                        :
                        moment().subtract(30, 'day').startOf('day').valueOf(),
                ), // to limit to recent orders only
                where('settlementDate', '==', null),
            ),
            async (snapshot) => {
                console.log('=================================');
                console.log(`${Collections.UserOrder} order queue changed!`);
                console.log('=================================');

                var userOrdersQueue = [];

                if (snapshot && !snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = {
                            ...snapshot.docs[i].data(),

                            notifyAt: Math.max(snapshot.docs[i].data().updatedAt, snapshot.docs[i].data().notifyAt || 0, snapshot.docs[i].data().createdAt),
                        };

                        if (
                            record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                            record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                            // &&
                            // record.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                        ) {
                            // skip order if already paid (since paid order will turn order to prepared) and all its items havent delivered yet
                            if (record.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED &&
                                record.paymentDetails !== null
                            ) {
                                let isAllChecked = true;
                                for (let j = 0; j < record.cartItems.length; j++) {
                                    if (record.cartItems[j].deliveredAt === null) {
                                        isAllChecked = false;
                                        break;
                                    }
                                }

                                if (isAllChecked) {
                                    userOrdersQueue.push(record);
                                }
                            }
                            else {
                                userOrdersQueue.push(record);
                            }
                        }
                    }
                }

                CommonStore.update((s) => {
                    s.userOrdersQueue = userOrdersQueue;
                });
            }
        );

        subscriberUserOrderPreparing = onSnapshot(
            query(
                collection(global.db, Collections.UserOrder),
                where('outletId', '==', currOutletId),
                where('orderStatus', 'in', [
                    USER_ORDER_STATUS.ORDER_RECEIVED,
                    USER_ORDER_STATUS.ORDER_AUTHORIZED,
                    USER_ORDER_STATUS.ORDER_PREPARING,
                    USER_ORDER_STATUS.ORDER_PREPARED,
                    // USER_ORDER_STATUS.ORDER_DELIVERED,

                    // USER_ORDER_STATUS.ORDER_COMPLETED,

                    // USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
                    // USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                    // USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,

                    // USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                    // USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                ]),
                // where('createdAt', '>=', moment().subtract(1, 'day')
                //     .startOf('day').valueOf()) //current set to 1 day
                where(
                    'createdAt',
                    '>=',
                    !toggleOpenOrder
                        ?
                        moment().subtract(1, 'day').startOf('day').valueOf()
                        :
                        moment().subtract(30, 'day').startOf('day').valueOf(),
                ), // to limit to recent orders only
                where('settlementDate', '==', null),
            ),
            async (snapshot) => {
                console.log('=================================');
                console.log(`${Collections.UserOrder} order queue changed!`);
                console.log('=================================');

                var userOrdersPreparing = [];

                if (snapshot && !snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = {
                            ...snapshot.docs[i].data(),

                            notifyAt: Math.max(snapshot.docs[i].data().updatedAt, snapshot.docs[i].data().notifyAt || 0, snapshot.docs[i].data().createdAt),
                        };

                        if (
                            record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                            record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                        ) {
                            // add order to list if already paid (since paid order will turn order to prepared), but items havent fully delivered
                            if (record.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED &&
                                record.paymentDetails !== null
                            ) {
                                let isAllChecked = true;
                                for (let j = 0; j < record.cartItems.length; j++) {
                                    if (record.cartItems[j].deliveredAt === null) {
                                        isAllChecked = false;
                                        break;
                                    }
                                }

                                if (isAllChecked) {
                                    // skip, already all delivered
                                }
                                else {
                                    userOrdersPreparing.push(record);
                                }
                            }
                            else {
                                if (record.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                                    // already prepare, can skip
                                }
                                else {
                                    userOrdersPreparing.push(record);
                                }
                            }
                        }
                    }
                }

                CommonStore.update((s) => {
                    s.userOrdersPreparing = userOrdersPreparing;
                });
            }
        );

        return () => {
            subscriberUserOrderQueue();
            subscriberUserOrderPreparing();
        };
    } catch (error) {
        console.log('failed listen user order')
        console.log(error);
    }

};

export const listenToGlobalPointsChanges = async (email, userPhone, selectOutletToShowPoint) => {
    // 2023-09-11 - Hide first

    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserOutletDict),
    //         where('userPhone', '==', userPhone),

    //     ),
    //     async (snapshot) => {

    //         var userOutletDictTemp = [];
    //         var visitedOutletIdList = [];
    //         var visitedOutletNameList = [];
    //         //////////////////////////////////////////////////////////////////////////////
    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 userOutletDictTemp.push(record);
    //                 visitedOutletIdList.push(record.visitedOutletIdList);
    //                 visitedOutletNameList.push(record.visitedOutletNameList);
    //             }
    //         }

    //         if (snapshot) {
    //             UserStore.update((s) => {
    //                 s.userOutletDict = userOutletDictTemp;
    //                 s.visitedOutletIdList = visitedOutletIdList;
    //                 s.visitedOutletNameList = visitedOutletNameList;
    //             });
    //         }
    //     });
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserPointsTransaction),
    //         where('email', '==', email),
    //         where('outletId', '==', selectOutletToShowPoint),
    //         where('deletedAt', '==', null),

    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserPointsTransaction} changed!`);

    //         var selectedCustomerGlobalPointsTransactionsEmail = [];
    //         var selectedCustomerGlobalPointsBalanceEmail = 0;
    //         //////////////////////////////////////////////////////////////////////////////

    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 selectedCustomerGlobalPointsTransactionsEmail.push(record);
    //                 selectedCustomerGlobalPointsBalanceEmail += record.amount;
    //             }
    //         }

    //         selectedCustomerGlobalPointsTransactionsEmail.sort(
    //             (a, b) => b.createdAt - a.createdAt,
    //         );

    //         if (snapshot) {
    //             UserStore.update((s) => {
    //                 s.selectedCustomerGlobalPointsTransactionsEmail = selectedCustomerGlobalPointsTransactionsEmail;
    //                 s.selectedCustomerGlobalPointsBalanceEmail = selectedCustomerGlobalPointsBalanceEmail;
    //             });
    //         }
    //     });
    // onSnapshot(
    //     query(
    //         collection(global.db, Collections.UserPointsTransaction),
    //         where('phone', '==', userPhone),
    //         where('outletId', '==', selectOutletToShowPoint),
    //         where('deletedAt', '==', null),

    //     ),
    //     async (snapshot) => {
    //         console.log(`${Collections.UserPointsTransaction} changed!`);

    //         var selectedCustomerGlobalPointsTransactionsPhone = [];
    //         var selectedCustomerGlobalPointsBalancePhone = 0;
    //         //////////////////////////////////////////////////////////////////////////////

    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 selectedCustomerGlobalPointsTransactionsPhone.push(record);
    //                 selectedCustomerGlobalPointsBalancePhone += record.amount;
    //             }
    //         }

    //         selectedCustomerGlobalPointsTransactionsPhone.sort(
    //             (a, b) => b.createdAt - a.createdAt,
    //         );

    //         if (snapshot) {
    //             UserStore.update((s) => {
    //                 s.selectedCustomerGlobalPointsTransactionsPhone = selectedCustomerGlobalPointsTransactionsPhone;
    //                 s.selectedCustomerGlobalPointsBalancePhone = selectedCustomerGlobalPointsBalancePhone;
    //             });
    //         }
    //     });
};

export const listenToSelectedUserChanges = async (userPhone) => {
    // firebase.firestore()
    //     .collection(Collections.UserOrder)
    //     .where('tableId', '==', selectedOutletTableId)
    //     .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
    //     // .where('outletId', '==', selectedOutletId)
    //     // .where('userId', '==', userId)
    //     // .limit(1)
    //     .onSnapshot(async snapshot => {
    //         console.log(`${Collections.UserOrder} changed!`);
    //         console.log(snapshot);

    //         var selectedTableOrders = [];

    //         if (!snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 if (
    //                     record.orderStatus !==
    //                     USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
    //                     record.orderStatus !==
    //                     USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
    //                 ) {
    //                     selectedTableOrders.push(record);
    //                 }
    //             }
    //         }

    //         CommonStore.update(s => {
    //             s.selectedTableOrders = selectedTableOrders;
    //         });
    //     });
};

// export const requestNotificationsPermission = async () => {
//     const authStatus = await firebase.messaging()().requestPermission();
//     const enabled =
//         authStatus === firebase.messaging().AuthorizationStatus.AUTHORIZED ||
//         authStatus === firebase.messaging().AuthorizationStatus.PROVISIONAL;

//     if (enabled) {
//         console.log('Authorization status:', authStatus);
//     }
// };

const pad = (n) => { return ("00000000" + n).substr(-8); };
const naturalExpand = (a) => { return a.replace(/\d+/g, pad) };
export const naturalCompare = (a, b) => {
    return naturalExpand(a).localeCompare(naturalExpand(b));
};

export const getOutletById = async (outletId) => {
    // const outletSnapshot = await firebase.firestore()
    //     .collection(Collections.Outlet)
    //     .where('uniqueId', '==', outletId)
    //     .limit(1)
    //     .get();

    const outletSnapshot = await getDocs(
        query(
            collection(global.db, Collections.Outlet),
            where('uniqueId', '==', outletId),
            limit(1),
        )
    );

    var outlet = null;
    if (!outletSnapshot.empty) {
        outlet = outletSnapshot.docs[0].data();
    }

    return outlet;
};

export const isMobile = () => {
    // credit to Timothy Huang for this regex test:
    // https://dev.to/timhuang/a-simple-way-to-detect-if-browser-is-on-a-mobile-device-with-javascript-44j3
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        return true
    }
    else {
        if (global.toggleMobile) {
            return true;
        }
        else {
            return false;
        }
    };
};

export const getCachedUrlContent = async (urlAsKey, expireInMinutes = 43800, object) => {
    var updatedAt = Date.now();
    if (object && object.updatedAt) {
        updatedAt = object.updatedAt;
    }

    let data = null;

    await AsyncStorage.getItem(urlAsKey, async (err, value) => {

        data = (JSON.parse(value));

        // there is data in cache && cache is expired
        if (data !== null && data['expireAt'] &&
            new Date(data.expireAt) < (new Date())) {

            //clear cache
            AsyncStorage.removeItem(urlAsKey);


            //update res to be null
            data = null;
        } else {

            console.log('read data from cache  ');

        }
    });

    //update cache + set expire at date
    if (data === null || (data && data.updatedAt && data.updatedAt !== updatedAt)) {
        console.log('cache new Date ');

        //fetch data

        await getImageFromFirebaseStorage(urlAsKey, async (parsedUrl) => {
            var apiRes = {};

            apiRes.parsedUrl = parsedUrl;
            apiRes.updatedAt = updatedAt;

            //set expire at
            apiRes.expireAt = getExpireDate(expireInMinutes);

            //stringify object
            const objectToStore = JSON.stringify(apiRes);

            //store object
            AsyncStorage.setItem(urlAsKey, objectToStore);

            console.log(apiRes.expireAt);

            return apiRes;
        });


        // data = fetch(urlAsKey).then((response) => response.json())
        //     .then(apiRes => {

        //         //set expire at
        //         apiRes.expireAt = getExpireDate(expireInMinutes);

        //         //stringify object
        //         const objectToStore = JSON.stringify(apiRes);

        //         //store object
        //         AsyncStorage.setItem(urlAsKey, objectToStore);


        //         console.log(apiRes.expireAt);
        //         return apiRes;
        //     });

    }

    return data;
};

/**
 *
 * @param expireInMinutes
 * @returns {Date}
 */
const getExpireDate = (expireInMinutes) => {
    const now = new Date();
    let expireTime = new Date(now);
    expireTime.setMinutes(now.getMinutes() + expireInMinutes);
    return expireTime;
};

export const mondayFirst = (dayIndex) => {
    return (dayIndex - 1 >= 0) ? (dayIndex - 1) : 6;
};

export const checkApplyDiscountPerValidity = (promotion, promotionIdList, cartItem) => {
    if (cartItem.promotionId) {
        return {
            validity: false,
            discountQuantity: 1,
        };
    }

    if (promotion.applyDiscountPer === APPLY_DISCOUNT_PER.ORDER || promotion.applyDiscountPer === undefined) {
        if (promotionIdList.includes(promotion.promotionId)) {
            return {
                validity: false,
                discountQuantity: 1,
            };
        }
        else {
            return {
                validity: true,
                discountQuantity: 1,
            };
        }
    }
    else {
        return {
            validity: true,
            discountQuantity: cartItem.quantity,
        };
    }
};

// check earlier for the rest of cart items, to see if the min quantity and price, matched (for whole bill purpose)
export const checkQualifiedItemsQuantityAndAmountForPromotion = async (
    allowStackedPromotionVoucher = false,
    applyDiscountType = APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
    campaignData = [],
    selectedPromoCodePromotion = {},
    selectedTaggableVoucher = {},
    promotionIdAppliedList = [],

    promotionDict,
    selectedOutletItemCategoriesDict,

    cartItems,
    usePromoCode = true,

    outletItems,
    outletsItemAddOnIdDict,
    outletsItemAddOnChoiceIdDict,
    orderType,
    currOutlet,

    orderTypeSub = ORDER_TYPE_SUB.NORMAL,
) => {
    let cartItemsInfo = {
        quantity: 0,
        price: 0,
    };

    var tempCartOutletItemsDict = {};

    var tempCartOutletItemAddOnDict = {};
    var tempCartOutletItemAddOnChoiceDict = {};

    for (let i = 0; i < cartItems.length; i++) {
        const tempCartItem = cartItems[i];

        ///////////////////////////////////////////    

        if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
            let foundItem = outletItems.find(findItem => findItem.uniqueId === tempCartItem.itemId);

            if (foundItem) {
                tempCartOutletItemsDict[tempCartItem.itemId] =
                    foundItem;
            }
        }

        var tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price;

        var extraPrice = 0;
        if (orderType === ORDER_TYPE.DELIVERY &&
            currOutlet &&
            currOutlet.deliveryPrice) {
            extraPrice = currOutlet.deliveryPrice;
        }
        else if (orderType === ORDER_TYPE.PICKUP &&
            currOutlet &&
            currOutlet.pickUpPrice) {
            extraPrice = currOutlet.pickUpPrice;
        }

        if (orderType === ORDER_TYPE.DELIVERY) {
            extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].deliveryCharges || 0;

            if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
            }

            if (!tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesActive) {
                extraPrice = 0;
            }
        }

        if (orderType === ORDER_TYPE.PICKUP) {
            extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].pickUpCharges || 0;

            if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
            }

            if (!tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesActive) {
                extraPrice = 0;
            }
        }

        var tempCartItemPriceOriginal =
            BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

        //////////////////////////////////////////////////////////

        if (tempCartItem.priceVariable !== undefined) {
            tempCartItemPrice = tempCartItem.priceVariable;

            tempCartItemPriceOriginal = tempCartItem.priceVariable;
        }

        //////////////////////////////////////////////////////////

        if (tempCartItem.priceVariable !== undefined) {
            tempCartItemPrice = tempCartItem.priceVariable;

            tempCartItemPriceOriginal = tempCartItem.priceVariable;
        }

        if (tempCartItem.priceUpselling !== undefined) {
            tempCartItemPrice = tempCartItem.priceUpselling;

            tempCartItemPriceOriginal = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
        }

        ///////////////////////////////////////////

        if (tempCartItem.priceVariable === undefined) {
            if (tempCartItem.priceUpselling === undefined) {
                tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
            }
        }
        else {
            tempCartItemPrice = tempCartItem.priceVariable;
        }

        //////////////////////////////////////////////////////////

        if (tempCartItem.choices) {
            const tempCartItemChoices = Object.entries(tempCartItem.choices).map(
                ([key, value]) => ({ key: key, value: value })
            );

            for (var j = 0; j < tempCartItemChoices.length; j++) {
                if (tempCartItemChoices[j].value) {
                    // means the addon of this item is picked, need to retrieve the actual addon

                    if (
                        tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] ===
                        undefined
                    ) {
                        // const outletItemAddOnChoiceSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.OutletItemAddOnChoice)
                        //   .where("uniqueId", "==", tempCartItemChoices[j].key)
                        //   .limit(1)
                        //   .get();

                        // if (!outletItemAddOnChoiceSnapshot.empty) {
                        //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                        //     outletItemAddOnChoiceSnapshot.docs[0].data();
                        // }

                        // console.log('===================');
                        // console.log('global.cartOutletItemAddOnChoiceDict');
                        // console.log(global.cartOutletItemAddOnChoiceDict);
                        // console.log('global.outletsItemAddOnChoiceIdDict');
                        // console.log(global.outletsItemAddOnChoiceIdDict);
                        // console.log('===================');

                        // console.log('outletsItemAddOnChoiceIdDict');
                        // console.log(outletsItemAddOnChoiceIdDict);
                        // console.log('tempCartItemChoices[j].key');
                        // console.log(tempCartItemChoices[j].key);
                        // console.log('outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]');
                        // console.log(outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]);

                        if (global.outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                            tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                                global.outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key];
                        }
                        else {
                            const outletItemAddOnChoiceSnapshot = await getDocs(
                                query(
                                    collection(global.db, Collections.OutletItemAddOnChoice),
                                    where('uniqueId', '==', tempCartItemChoices[j].key),
                                )
                            );

                            if (outletItemAddOnChoiceSnapshot && !outletItemAddOnChoiceSnapshot.empty) {
                                console.log('============================');
                                console.log('============================');
                                console.log('============================');
                                console.log('tempCartOutletItemAddOnChoiceDict');
                                console.log(tempCartOutletItemAddOnChoiceDict);
                                console.log('tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]');
                                console.log(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]);
                                console.log('[tempCartItemChoices[j].key]');
                                console.log([tempCartItemChoices[j].key]);
                                console.log('outletItemAddOnChoiceSnapshot.docs[0].data()');
                                console.log(outletItemAddOnChoiceSnapshot.docs[0].data());
                                console.log('key testing');
                                console.log('============================');
                                console.log('============================');
                                console.log('============================');

                                tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                                    outletItemAddOnChoiceSnapshot.docs[0].data();
                            }

                            global.outletsItemAddOnChoiceIdDict = {
                                ...global.outletsItemAddOnChoiceIdDict,

                                ...tempCartOutletItemAddOnChoiceDict,
                            };

                            let cachedAddOnChoiceIdDictTemp = {
                                // ...cachedAddOnChoiceIdDict,

                                ...outletsItemAddOnChoiceIdDict,
                            };

                            CommonStore.update(s => {
                                s.cachedAddOnChoiceIdDict = cachedAddOnChoiceIdDictTemp;
                            });

                            tempCartOutletItemAddOnChoiceDict = global.outletsItemAddOnChoiceIdDict;

                            // if (outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                            //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                            //     outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key];
                            // }

                            tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] = outletItemAddOnChoiceSnapshot.docs[0].data();

                            // 2023-06-17 - original
                            // if (outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                            //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                            //     outletsItemAd
                        }
                    }

                    if (tempCartItem.priceVariable === undefined) {
                        // tempCartItemPrice +=
                        //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                        //     .price;

                        // console.log('tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j]]');
                        // console.log(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j]]);

                        // if (tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] === undefined) {
                        //     // 2023-11-01 - something wrong here

                        //     alert('Please rescan the QR code to proceed.');
                        // }

                        tempCartItemPrice =
                            BigNumber(tempCartItemPrice).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                                .price).toNumber();

                        // 2022-10-05 - Added missing line

                        // tempCartItemPriceOriginal +=
                        //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                        //     .price;

                        tempCartItemPriceOriginal =
                            BigNumber(tempCartItemPriceOriginal).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key].price).toNumber();
                    }

                    // need to retrieve the description/type name of this addon choice

                    const tempCartItemAddOnChoice =
                        tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key];

                    if (
                        tempCartOutletItemAddOnDict[
                        tempCartItemAddOnChoice.outletItemAddOnId
                        ] === undefined
                    ) {
                        // const outletItemAddOnSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.OutletItemAddOn)
                        //   .where(
                        //     "uniqueId",
                        //     "==",
                        //     tempCartItemAddOnChoice.outletItemAddOnId
                        //   )
                        //   .limit(1)
                        //   .get();

                        // if (!outletItemAddOnSnapshot.empty) {
                        //   tempCartOutletItemAddOnDict[
                        //     tempCartItemAddOnChoice.outletItemAddOnId
                        //   ] = outletItemAddOnSnapshot.docs[0].data();
                        // }

                        // console.log('outletsItemAddOnIdDict');
                        // console.log(outletsItemAddOnIdDict);

                        if (outletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId]) {
                            tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] =
                                outletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId];
                        }
                    }

                    // if (
                    //     tempCartItemAddOnCategorized[
                    //     tempCartItemAddOnChoice.outletItemAddOnId
                    //     ] === undefined
                    // ) {
                    //     tempCartItemAddOnCategorized[
                    //         tempCartItemAddOnChoice.outletItemAddOnId
                    //     ] = [];
                    // }

                    // tempCartItemAddOnCategorized[
                    //     tempCartItemAddOnChoice.outletItemAddOnId
                    // ].push(tempCartItemAddOnChoice.name);

                    // if (
                    //     tempCartItemAddOnCategorizedPrice[
                    //     tempCartItemAddOnChoice.outletItemAddOnId
                    //     ] === undefined
                    // ) {
                    //     tempCartItemAddOnCategorizedPrice[
                    //         tempCartItemAddOnChoice.outletItemAddOnId
                    //     ] = [];
                    // }

                    // tempCartItemAddOnCategorizedPrice[
                    //     tempCartItemAddOnChoice.outletItemAddOnId
                    // ].push(tempCartItemAddOnChoice.price);
                }
            }

            // const tempCartItemAddOnCategorizedList = Object.entries(
            //     tempCartItemAddOnCategorized
            // ).map(([key, value]) => ({ key: key, value: value }));
            // const tempCartItemAddOnCategorizedPriceList = Object.entries(
            //     tempCartItemAddOnCategorizedPrice
            // ).map(([key, value]) => ({ key: key, value: value }));

            // if (tempCartItemAddOnCategorizedList.length > 0) {
            //     for (var j = 0; j < tempCartItemAddOnCategorizedList.length; j++) {
            //         const tempCartItemAddOnName =
            //             tempCartOutletItemAddOnDict[
            //                 tempCartItemAddOnCategorizedList[j].key
            //             ].name;

            //         tempCartItemAddOnParsed.push({
            //             name: tempCartItemAddOnName,
            //             choiceNames: tempCartItemAddOnCategorizedList[j].value,
            //             prices: tempCartItemAddOnCategorizedPriceList[j].value,
            //         });
            //     }
            // }
        }

        //////////////////////////////////////////////////////////////////////

        // for add-on group

        if (tempCartItem.addOnGroupList) {
            for (var j = 0; j < tempCartItem.addOnGroupList.length; j++) {
                // now separate all, might change in future

                const addOnGroup = tempCartItem.addOnGroupList[j];

                // tempCartItemAddOnParsed.push({
                //     name: addOnGroup.addOnName,
                //     addOnId: addOnGroup.outletItemAddOnId,
                //     choiceNames: [addOnGroup.choiceName],
                //     prices: [addOnGroup.quantity * addOnGroup.price],
                //     quantities: [addOnGroup.quantity],
                //     singlePrices: [addOnGroup.price],
                //     addOnChoiceIdList: [addOnGroup.outletItemAddOnChoiceId],
                //     minSelectList: [addOnGroup.minSelect],
                //     maxSelectList: [addOnGroup.maxSelect],
                // });

                if (tempCartItem.priceVariable === undefined) {
                    // tempCartItemPrice += addOnGroup.quantity * addOnGroup.price;
                    // tempCartItemPriceOriginal += addOnGroup.quantity * addOnGroup.price;

                    tempCartItemPrice = BigNumber(tempCartItemPrice).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
                    tempCartItemPriceOriginal = BigNumber(tempCartItemPriceOriginal).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
                }
            }
        }

        //////////////////////////////////////////////////////////////////////

        // 2022-10-05 - Fixes for variable pricing (should use the inputed price already)

        if (tempCartItem.priceVariable === undefined) {
            // tempCartItemPrice = tempCartItemPrice * tempCartItem.quantity;
            // tempCartItemPriceOriginal =
            //   tempCartItemPriceOriginal * tempCartItem.quantity;

            tempCartItemPrice = BigNumber(tempCartItemPrice).multipliedBy(tempCartItem.quantity).toNumber();
            tempCartItemPriceOriginal =
                BigNumber(tempCartItemPriceOriginal).multipliedBy(tempCartItem.quantity).toNumber();
        }

        //////////////////////////////////////////////////////////////////////

        var promotionCategory = undefined;

        if (
            selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
            promotionDict[
            selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ] !== undefined
            && promotionDict[
                selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
            ].usePromoCode === usePromoCode
            &&
            checkIsAllowPromotionVoucherToApply(
                allowStackedPromotionVoucher,
                applyDiscountType,
                promotionDict[
                selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
                ],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedList,
            )
        ) {
            promotionCategory =
                promotionDict[
                selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
                ];
        }

        if (promotionDict[tempCartItem.itemSku] !== undefined
            && promotionDict[tempCartItem.itemSku].usePromoCode === usePromoCode
            &&
            checkIsAllowPromotionVoucherToApply(
                allowStackedPromotionVoucher,
                applyDiscountType,
                promotionDict[tempCartItem.itemSku],
                selectedPromoCodePromotion,
                {},
                promotionIdAppliedList,
            )
            // &&
            // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
        ) {
            var promotionResult = checkApplyDiscountPerValidity(promotionDict[tempCartItem.itemSku], promotionIdAppliedList, tempCartItem);

            if (promotionResult.validity) {
                // here accum the amount and quantity

                cartItemsInfo.quantity += tempCartItem.quantity;
                cartItemsInfo.price += tempCartItemPrice;
            }
        } else if (promotionCategory !== undefined) {
            var promotionResult = checkApplyDiscountPerValidity(promotionCategory, promotionIdAppliedList, tempCartItem);

            if (promotionResult.validity) {
                // here accum the amount and quantity

                cartItemsInfo.quantity += tempCartItem.quantity;
                cartItemsInfo.price += tempCartItemPrice;
            }
        }
    }

    return cartItemsInfo;
};

export const parseValidPriceText = (text) => {
    if (text.indexOf('.') > 0) {
        var strAfter = text.slice(text.indexOf('.'));

        if (strAfter.length > 2) {
            return parseFloat(text).toFixed(2);
        }
        else {
            return text;
        }
    }
    else {
        return text;
    }
};

export const signInWithPhoneForCRMUser = async (selectedOutlet, params = {
    nameVerification: false,
    name: '',
    phone: '',

    // cb: () => {},
}) => {
    var userNameTemp = '';
    var userEmailTemp = '';
    var userNumberTemp = '';
    var userFirebaseUidTemp = '';
    var userUserIdTemp = '';
    var gReview = false;

    if (selectedOutlet && selectedOutlet.uniqueId) {
        var storedUserName = await AsyncStorage.getItem('storedUserName');
        var storedUserPhone = await AsyncStorage.getItem('storedUserPhone');

        if (global.storedUserName && global.storedUserPhone) {
            storedUserName = global.storedUserName;
            storedUserPhone = global.storedUserPhone;
        }

        if (params) {
            if (params.name) {
                storedUserName = params.name;
            }

            if (params.phone) {
                storedUserPhone = params.phone;
            }
        }

        if (
            // storedUserName
            // &&
            storedUserPhone) {
            // const crmUserSnapshot = await firebase
            //     .firestore()
            //     .collection(Collections.CRMUser)
            //     .where("outletId", "==", selectedOutlet.uniqueId)
            //     .where('number', '==', storedUserPhone)
            //     .limit(1)
            //     .get();

            let crmUserSnapshot = null;

            if (params.nameVerification) {
                crmUserSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.CRMUser),
                        where("outletId", "==", selectedOutlet.uniqueId),
                        where('number', '==', storedUserPhone),
                        where('name', '==', storedUserName),
                        limit(1),
                    )
                );
            }
            else {
                crmUserSnapshot = await getDocs(
                    query(
                        collection(global.db, Collections.CRMUser),
                        where("outletId", "==", selectedOutlet.uniqueId),
                        where('number', '==', storedUserPhone),
                        limit(1),
                    )
                );
            }

            if (crmUserSnapshot && !crmUserSnapshot.empty) {
                // means got this user

                var crmUser = crmUserSnapshot.docs[0].data();

                userNameTemp = crmUser.name;
                userEmailTemp = crmUser.email;
                userNumberTemp = crmUser.number;
                userFirebaseUidTemp = crmUser.userId ? crmUser.userId : crmUser.email;
                userUserIdTemp = crmUser.userId ? crmUser.userId : crmUser.email;
                gReview = crmUser.gReview ? crmUser.gReview : false;

                let epStateTo = crmUser.epStateTo ? crmUser.epStateTo : '';
                let epNameTo = crmUser.epNameTo ? crmUser.epNameTo : '';
                let epPhoneTo = crmUser.epPhoneTo ? crmUser.epPhoneTo : '';
                let epAddr1To = crmUser.epAddr1To ? crmUser.epAddr1To : '';
                let epCityTo = crmUser.epCityTo ? crmUser.epCityTo : '';
                let epCodeTo = crmUser.epStateTo ? crmUser.epCodeTo : '';

                let epTinTo = crmUser.tin ? crmUser.tin : '';
                let epEmailTo = crmUser.emailSecond ? crmUser.emailSecond : '';
                let epIdTo = crmUser.eiId ? crmUser.eiId : '';
                let epIdTypeTo = crmUser.eiIdType ? crmUser.eiIdType : '';

                CommonStore.update(s => {
                    s.currCrmUser = {
                        ...crmUser,
                        userName: crmUser.name,
                        userEmail: crmUser.email,
                        userNumber: crmUser.number,
                        userId: crmUser.userId ? crmUser.userId : crmUser.email,
                        userAddress: crmUser.address ? crmUser.address : '',
                        gReview: crmUser.gReview ? crmUser.gReview : false,

                        epStateTo,
                        epNameTo,
                        epPhoneTo,
                        epAddr1To,
                        epCityTo,
                        epCodeTo,

                        epTinTo,
                        epEmailTo,
                        epIdTo,
                        epIdTypeTo,
                    };
                });

                UserStore.update(s => {
                    s.name = userNameTemp;
                    s.email = userEmailTemp;
                    s.number = userNumberTemp;
                    s.firebaseUid = userFirebaseUidTemp;
                    s.userId = userUserIdTemp;
                    s.gReview = gReview;
                    s.levelName = crmUser.levelName ? crmUser.levelName : '-';

                    s.avatar = crmUser.avatar ? crmUser.avatar : '';
                    s.dob = crmUser.dob ? crmUser.dob : '';
                    s.gender = crmUser.gender ? crmUser.gender : 'Male';
                    s.outletId = crmUser.outledId ? crmUser.outletId : '';
                    s.race = crmUser.race ? crmUser.race : '';
                    s.state = crmUser.state ? crmUser.state : '';
                    s.uniqueName = crmUser.uniqueName ? crmUser.uniqueName : '';
                    s.updatedAt = crmUser.updatedAt ? crmUser.updatedAt : Date.now();
                    s.levelName = crmUser.levelName ? crmUser.levelName : '';
                    s.levelOrderIndex = crmUser.levelOrderIndex ? crmUser.levelOrderIndex : -1;

                    s.epStateTo = epStateTo;
                    s.epNameTo = epNameTo;
                    s.epPhoneTo = epPhoneTo;
                    s.epAddr1To = epAddr1To;
                    s.epCityTo = epCityTo;
                    s.epCodeTo = epCodeTo;

                    s.epTinTo = epTinTo;
                    s.epEmailTo = epEmailTo;
                    s.epIdTo = epIdTo;
                    s.epIdTypeTo = epIdTypeTo;

                    s.crmUserId = crmUser.uniqueId;
                    s.userGroups = crmUser.userGroups || ['EVERYONE'];
                });

                global.crmUser = crmUser;

                global.userPhone = userNumberTemp;

                global.userPhoneLoaded = true;

                global.toShowSignUpMemberModal = false;

                TempStore.update(s => {
                    s.checkedSignInWithPhone = 'yes';
                });

                setTimeout(() => {
                    try {
                        const commonUserData = {
                            epStateTo,
                            epNameTo,
                            epPhoneTo,
                            epAddr1To,
                            epCityTo,
                            epCodeTo,

                            epTinTo,
                            epEmailTo,
                            epIdTo,
                            epIdTypeTo,
                        };

                        secureLocalStorage.setItem('commonUserData', commonUserData);
                    }
                    catch (ex) {
                        console.error(ex);
                    }
                }, 100);

                return {
                    name: crmUser.name,
                    phone: crmUser.number,
                };
            }
            else {
                TempStore.update(s => {
                    s.checkedSignInWithPhone = 'no';
                });

                return false;
            }
        }
        else {
            TempStore.update(s => {
                s.checkedSignInWithPhone = 'no';
            });

            return false;
        }
    }
    else {
        return false;
    }
};

(function () {
    'use strict';

    let DEFAULT_MAX_DEPTH = 6;
    let DEFAULT_ARRAY_MAX_LENGTH = 50;
    let seen; // Same variable used for all stringifications

    Date.prototype.toPrunedJSON = Date.prototype.toJSON;
    String.prototype.toPrunedJSON = String.prototype.toJSON;

    let cx =
        /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
        escapable =
            /[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
        meta = {
            // table of character substitutions
            '\b': '\\b',
            '\t': '\\t',
            '\n': '\\n',
            '\f': '\\f',
            '\r': '\\r',
            '"': '\\"',
            '\\': '\\\\',
        };

    function quote(string) {
        escapable.lastIndex = 0;
        return escapable.test(string)
            ? '"' +
            string.replace(escapable, function (a) {
                let c = meta[a];
                return typeof c === 'string'
                    ? c
                    : '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
            }) +
            '"'
            : '"' + string + '"';
    }

    function str(key, holder, depthDecr, arrayMaxLength) {
        let i, // The loop counter.
            k, // The member key.
            v, // The member value.
            length,
            partial,
            value = holder[key];
        if (
            value &&
            typeof value === 'object' &&
            typeof value.toPrunedJSON === 'function'
        ) {
            value = value.toPrunedJSON(key);
        }

        switch (typeof value) {
            case 'string':
                return quote(value);
            case 'number':
                return isFinite(value) ? String(value) : 'null';
            case 'boolean':
            case 'null':
                return String(value);
            case 'object':
                if (!value) {
                    return 'null';
                }
                if (depthDecr <= 0 || seen.indexOf(value) !== -1) {
                    // return '"-pruned-"';
                    return JSON.stringify(value);
                }
                seen.push(value);
                partial = [];
                if (Object.prototype.toString.apply(value) === '[object Array]') {
                    length = Math.min(value.length, arrayMaxLength);
                    for (i = 0; i < length; i += 1) {
                        partial[i] = str(i, value, depthDecr - 1, arrayMaxLength) || 'null';
                    }
                    v = partial.length === 0 ? '[]' : '[' + partial.join(',') + ']';
                    return v;
                }
                for (k in value) {
                    if (Object.prototype.hasOwnProperty.call(value, k)) {
                        try {
                            v = str(k, value, depthDecr - 1, arrayMaxLength);
                            if (v) partial.push(quote(k) + ':' + v);
                        } catch (e) {
                            // this try/catch due to some "Accessing selectionEnd on an input element that cannot have a selection." on Chrome
                        }
                    }
                }
                v = partial.length === 0 ? '{}' : '{' + partial.join(',') + '}';
                return v;
        }
    }

    JSON.prune = function (value, depthDecr, arrayMaxLength) {
        seen = [];
        depthDecr = depthDecr || DEFAULT_MAX_DEPTH;
        arrayMaxLength = arrayMaxLength || DEFAULT_ARRAY_MAX_LENGTH;
        return str('', { '': value }, depthDecr, arrayMaxLength);
    };
})();

export const checkIsAllowPromotionVoucherToApply = (
    allowStackedPromotionVoucher = false,
    applyDiscountType = APPLY_DISCOUNT_TYPE.PROMOTION,
    campaignData = [],
    selectedPromoCodePromotion = {},
    selectedTaggableVoucher = {},
    promotionIdAppliedList = [],
) => {
    if (allowStackedPromotionVoucher) {
        return true;
    }
    else {
        if (applyDiscountType === APPLY_DISCOUNT_TYPE.PROMOTION) {
            // merchant's own promotion

            if (selectedPromoCodePromotion && selectedPromoCodePromotion.uniqueId) {
                return false;
            }
            else if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
                return false;
            }
            else {
                // check if got multiple merchant promotion (without promo code)

                if (promotionIdAppliedList.length > 1) {
                    // means already got 1

                    if (promotionIdAppliedList.includes(campaignData.promotionId)) {
                        // if is own one, can continue (might be per order/[item], that applied for multiple items)

                        return true;
                    }
                    else {
                        return false;
                    }
                }
                else {
                    return true;
                }
            }
        }
        else if (applyDiscountType === APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE) {
            // promo code promotion

            if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
                return false;
            }
            else {
                // check if got multiple merchant promotion (without promo code)

                // if (promotionIdAppliedList.length > 1) {
                //     // means already got 1

                //     if (promotionIdAppliedList.includes(campaignData.promotionId)) {
                //         // if is own one, can continue (might be per order/[item], that applied for multiple items)

                //         return true;
                //     }
                //     else {
                //         return false;
                //     }
                // }
                // else {
                //     return true;
                // }

                return true;
            }
        }
        else if (applyDiscountType === APPLY_DISCOUNT_TYPE.VOUCHER) {
            // voucher

            return true;
        }
    }
};

export const isTestingOutlet = () => {
    if (global.selectedOutlet && global.selectedOutlet.subdomain) {
        if (
            global.selectedOutlet.subdomain === 'hominsanttdi' ||
            global.selectedOutlet.subdomain === 'hominsan-ss15' ||
            global.selectedOutlet.subdomain === 'kenneth-cafe' ||
            global.selectedOutlet.subdomain === 'melvin-cafe' ||
            (global.selectedOutlet.subdomain && global.selectedOutlet.subdomain.startsWith('demo-cafe'))
        ) {
            return true;
        }
        else {
            return false;
        }
    }
    else {
        return false;
    }
};

export const uploadScanQrInfo = async (
    outletId,
    merchantId,

    tableQRUrl,

    tableId,
    waiterId,
    qrDateTimeEncrypted,

    // qrScanType,

    userIdAnonymous,
) => {
    const subcollectionId = `${tableId}_${waiterId}_${qrDateTimeEncrypted}`;

    const scannedQrRaw = await AsyncStorage.getItem('scannedQr');

    let scannedQrParsed = [];

    let isFirstTime = false;

    if (scannedQrRaw) {
        // existed

        scannedQrParsed = JSON.parse(scannedQrRaw);

        if (scannedQrParsed && typeof scannedQrParsed === 'object' &&
            scannedQrParsed.length >= 0) {
            // update it

            if (scannedQrParsed.includes(subcollectionId)) {
                // skip it
            }
            else {
                // proceed

                if (scannedQrParsed.length >= 30) {
                    // trim it                

                    scannedQrParsed = scannedQrParsed.slice(1);
                    scannedQrParsed.push(subcollectionId);
                }
                else {
                    // just append

                    scannedQrParsed.push(subcollectionId);
                }

                isFirstTime = true;
            }
        }
        else {
            // create it

            scannedQrParsed = [subcollectionId];

            await AsyncStorage.setItem('scannedQr', JSON.stringify(scannedQrParsed));

            isFirstTime = true;
        }
    }
    else {
        // create it

        scannedQrParsed = [subcollectionId];

        await AsyncStorage.setItem('scannedQr', JSON.stringify(scannedQrParsed));

        isFirstTime = true;
    }

    const body = {
        outletId: outletId,
        merchantId: merchantId,

        tableQRUrl: tableQRUrl,

        tableId: tableId,
        waiterId: waiterId,
        qrDateTimeEncrypted: qrDateTimeEncrypted,

        isFirstTime: isFirstTime,
    };

    ApiClient.POST(API.trackScanQrInfo, body).then((result) => {
        if (result && result.status === 'success') {
            console.log('success');
        }
    });
};

export const logEventCustomInit = async () => {
    if (global.kdSubdomain && global.kdIp && global.outletId) {
        const idToUsed = `${moment().format('YYYYMMDD')}${global.kdSubdomain}-${global.kdIp}`;

        await setDoc(
            doc(
                global.db, Collections.WebOrderUserActivity, idToUsed,
            ),
            {
                uniqueId: idToUsed,

                userName: global.userName ? global.userName : '',
                userPhone: global.userPhone ? global.userPhone : '',
                outletId: global.outletId ? global.outletId : '',
                merchantId: global.merchantId ? global.merchantId : '',

                startDayDate: moment().startOf('day').valueOf(),
                startMonthDate: moment().startOf('month').startOf('day').valueOf(),
                dateTimeStr: moment().format('YYYYMMDD'),

                createdAt: moment().valueOf(),
                updatedAt: moment().valueOf(),
                deletedAt: null,
            },
            {
                merge: true,
            }
        );
    }
    else {
        console.log('invalid');
    }
};

export const logEventCustom = async (
    screenName,
) => {
    // here will update

    if (global.kdSubdomain && global.kdIp && global.outletId && screenName) {
        const idToUsed = `${moment().format('YYYYMMDD')}${global.kdSubdomain}-${global.kdIp}`;

        await setDoc(
            doc(
                global.db, Collections.WebOrderUserActivity, idToUsed,
            ),
            {
                [moment().format('HHmmss')]: screenName,

                updatedAt: moment().valueOf(),
            },
            {
                merge: true,
            }
        );
    }
    else {
        console.log('invalid');
    }
};

export const updateWebTokenAnonymous = async (timeout = 5000) => {
    setTimeout(async () => {
        const userIdAnonymous = await AsyncStorage.getItem('userIdAnonymous');

        if (userIdAnonymous) {
            const webTokenAnonymousSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.WebTokenAnonymous),
                    where('uniqueId', '==', userIdAnonymous),
                    limit(1),
                )
            );

            let webTokenAnonymous = null;
            if (!webTokenAnonymousSnapshot.empty) {
                webTokenAnonymous = webTokenAnonymousSnapshot.docs[0].data();

                // if (
                //   webTokenAnonymous.FCMToken === currentToken
                //   ||
                //   (
                //     (webTokenAnonymous.userPhone === undefined || webTokenAnonymous.userPhone === '')
                //     &&
                //     userPhone
                //   )
                // ) {
                //   return;
                // }

                console.log(webTokenAnonymous.userPhone);
                console.log(global.userPhone)

                if (
                    // webTokenAnonymous.userPhone !== global.userPhone
                    true
                ) {
                    console.log('update the doc');

                    await setDoc(
                        doc(
                            global.db, Collections.WebTokenAnonymous, userIdAnonymous,
                        ),
                        {
                            // FCMToken: currentToken,

                            userPhone: global.userPhone,

                            userIdAnonymous: userIdAnonymous,

                            ...(typeof global.swSubscription === 'string') && {
                                swSub: typeof global.swSubscription === 'string' ? global.swSubscription : null,
                            },

                            updatedAt: Date.now(),
                        },
                        {
                            merge: true,
                        }
                    );
                }

                console.log("currentToken doc updated in WebTokenAnonymous", userIdAnonymous);
            }
        }
    }, timeout);
};

// 2024-04-12 - checking for required purchase item quantity
export const checkRPQtyStatus = (
    campaignData = {},
    cartItems = [],
    selectedOutletItemCategoriesDict = {},
    rpCategoryIdUsedPromoDict = undefined,
) => {
    let result = false;

    let currQty = 0;
    let matchedCartItems = [];

    if (
        campaignData.variationRp === undefined ||
        campaignData.variationItemsRp === undefined ||
        campaignData.variationItemsSkuRp === undefined ||
        campaignData.qtyRp === undefined ||
        campaignData.qtyRp <= 0 ||
        typeof campaignData.qtyRp !== 'number'
    ) {
        result = true;
    }
    else {
        for (let i = 0; i < cartItems.length; i++) {
            const tempCartItem = cartItems[i];

            if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                const tempCategory = selectedOutletItemCategoriesDict[tempCartItem.categoryId];

                if (campaignData.variationItemsSkuRp.includes(tempCategory.name)) {
                    currQty += tempCartItem.quantity;

                    matchedCartItems.push(tempCartItem);
                }
            }
            else if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                if (campaignData.variationItemsSkuRp.includes(tempCartItem.itemSku)) {
                    currQty += tempCartItem.quantity;

                    matchedCartItems.push(tempCartItem);
                }
            }

            ///////////////////////////////////////////
        }

        if (currQty >= campaignData.qtyRp) {
            result = true;
        }

        //////////////////////////

        // 2024-05-07 - required purchase changes

        let rpCategoryIdUsedPromoDictLocal = {
            ...global.rpCategoryIdUsedPromoDict,
        };
        if (rpCategoryIdUsedPromoDict) {
            rpCategoryIdUsedPromoDictLocal = rpCategoryIdUsedPromoDict;
        }

        let isStillConsumable = false;
        for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
            const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

            if (rpCategoryIdUsedPromoDictLocal[rpKey] === true) {
                // existed already, can skip
                continue;
            }
            else {
                // global.rpCategoryIdUsedPromoDict[rpKey] = true;
                isStillConsumable = true;
                break;
            }
        }

        if (!isStillConsumable) {
            result = false;
        }

        //////////////////////////
    }

    console.log(`checkRPQtyStatus result`);
    console.log(result);
    console.log(`currQty: ${currQty}`);
    console.log(`campaignData.qtyRp: ${campaignData.qtyRp}`);

    return {
        rpQtyStatus: result,
        rpQty: campaignData.qtyRp,
        currQty,
        matchedCartItems,
    };
};

export const checkToApplyTaxOrNot = (currOutlet, orderType, orderTypeSub = ORDER_TYPE_SUB.NORMAL) => {
    let status = false;

    if (orderTypeSub === undefined) {
        orderTypeSub = ORDER_TYPE_SUB.NORMAL;
    }

    if (currOutlet.taxActive &&
        (
            currOutlet.taxOrderTypes === undefined
            ||
            currOutlet.taxOrderTypes === ''
            ||
            (
                currOutlet.taxOrderTypes &&
                currOutlet.taxOrderTypes.length > 0 &&
                currOutlet.taxOrderTypes.includes(orderType))
        )
    ) {
        if (currOutlet.taxOrderTypes &&
            currOutlet.taxOrderTypes.length > 0) {
            if (orderType === ORDER_TYPE.PICKUP) {
                if (orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
                    status = true;
                }
                else {
                    // other delivery

                    status = false;
                }
            }
            else {
                status = true;
            }
        }
        else {
            status = true;
        }
    }

    return status;
};

export const checkToApplyScOrNot = (currOutlet, orderType, orderTypeSub = ORDER_TYPE_SUB.NORMAL, orderTypeDetails = ORDER_TYPE_DETAILS.POS) => {
    let status = false;

    if (orderTypeSub === undefined) {
        orderTypeSub = ORDER_TYPE_SUB.NORMAL;
    }

    if (currOutlet.scActive &&
        (
            currOutlet.scOrderTypes === undefined
            ||
            currOutlet.scOrderTypes === ''
            ||
            (
                currOutlet.scOrderTypes &&
                currOutlet.scOrderTypes.length > 0 &&
                currOutlet.scOrderTypes.includes(orderType))
        )
    ) {
        if (currOutlet.scOrderTypes &&
            currOutlet.scOrderTypes.length > 0) {
            if (orderType === ORDER_TYPE.PICKUP) {
                if (orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
                    status = true;
                }
                else {
                    // other delivery

                    status = false;
                }
            }
            else {
                status = true;
            }
        }
        else {
            status = true;
        }
    }

    return status;
};
export const isSafari = () => {
    // Get the user-agent string 
    let userAgentString =
        navigator.userAgent;

    // Detect Chrome 
    let chromeAgent = userAgentString.indexOf("Chrome") > -1;

    // Detect Safari 
    let safariAgent = userAgentString.indexOf("Safari") > -1;

    // Discard Safari since it also matches Chrome 
    if ((chromeAgent) && (safariAgent)) safariAgent = false;

    return safariAgent;
};

export const safariChecking = (cb = () => { }) => {
    let currUrl = window.location.href;

    if (isSafari()) {
        setTimeout(async () => {
            const reloadKeyRaw = await AsyncStorage.getItem('reloadKey');

            let toReload = false;

            if (reloadKeyRaw === null) {
                toReload = true;
            }
            else if (typeof reloadKeyRaw === 'string') {
                let reloadKeyVal = parseInt(reloadKeyRaw);

                console.log(`reloadKeyVal: ${reloadKeyVal}`);

                if (moment().diff(reloadKeyVal, 'second') >= 5) {
                    // means 5 seconds pass, only auto reload again

                    toReload = true;
                }
            }

            if (toReload) {
                await AsyncStorage.setItem('reloadKey', moment().valueOf().toString());

                window.location.href = currUrl;
                // window.location.reload();
            }

            cb();
        }, 1000);
    }
    else {
        cb();
    }
};

export const lazyRetry = (
    fn,
    { retries = 4, interval = 500, exponentialBackoff = true } = {}
) => {
    return new Promise((resolve, reject) => {
        fn()
            .then(resolve)
            .catch(error => {
                setTimeout(() => {
                    if (retries === 1) {
                        reject(error)

                        // try to reload the page if failed

                        return window.location.reload();
                    }

                    console.warn(`ChunkLoad failed. Will retry ${retries - 1} more times. Retrying...`)

                    // Passing on "reject" is the important part
                    lazyRetry(fn, {
                        retries: retries - 1,
                        interval: exponentialBackoff ? interval * 2 : interval
                    }).then(resolve, reject)
                }, interval)
            })
    })
};

export const checkIfVisibleItem = (outletItem) => {
    if (global.visibleCategories && global.visibleCategories.length > 0
        && global.visibleCategories.find(category => category.uniqueId === outletItem.categoryId)) {
        return true;
    }
    else {
        return false;
    }
};

/**
 * Generates a WhatsApp link with a phone number and message.
 * 
 * @param {string} phoneNumber - The recipient's phone number in international format (e.g., 1234567890).
 * @param {string} message - The message to encode.
 * @return {string} - The WhatsApp URL.
 */
export const getWhatsAppLink = (phoneNumber, message) => {
    const encodedMessage = encodeURIComponent(message).replace(/%20/g, "+");
    return `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
};

export const excludeSkipScItems = (
    totalSc,
    cartItems,
    scRate,
) => {
    let toDeductSc = 0;
    for (let i = 0; i < cartItems.length; i++) {
        let currCartItem = cartItems[i];
        let addOns = currCartItem.addOns ? currCartItem.addOns : currCartItem.addons;

        let toSkipSc = false;
        for (let j = 0; j < addOns.length; j++) {
            if (addOns[j].skipSc) {
                toSkipSc = true;
                break;
            }
        }

        if (toSkipSc) {
            if (toSkipSc) {
                toDeductSc = BigNumber(toDeductSc).plus(
                    (BigNumber(currCartItem.price).multipliedBy(scRate))
                ).toNumber();
            }
        }
    }

    if (BigNumber(toDeductSc).isGreaterThan(0)) {
        let totalScNew = Math.max(BigNumber(totalSc).minus(toDeductSc).toNumber(), 0);

        return totalScNew;
    }
    else {
        return totalSc;
    }
};

export const getClaimableVoucher = async ({
    anonymousDt,
    availableLoyaltyCampaigns,
    selectedOutletTaggableVouchersAll,
    selectedOutlet,
    userEmail,
    lastOrderFinalPrice = 0,
}) => {
    let claimableVoucherTemp = null;

    if (anonymousDt !== 'none') {
        if (moment().isSame(anonymousDt, 'day')) {
            // means same day visit, can proceed (to convert this customer into loyalty customer)
            for (let lcIndex = 0; lcIndex < availableLoyaltyCampaigns.length; lcIndex++) {
                const currLoyaltyCampaign = availableLoyaltyCampaigns[lcIndex];

                if (currLoyaltyCampaign.loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.RETURN && currLoyaltyCampaign.taggableVoucherId) {
                    // 2025-06-09 - Minimum Spent To Claim
                    if (currLoyaltyCampaign.minSpentToClaim) {
                        if (lastOrderFinalPrice >= currLoyaltyCampaign.minSpentToClaim) {
                            let outletTaggableVoucher = selectedOutletTaggableVouchersAll.find(voucher => voucher.uniqueId === currLoyaltyCampaign.taggableVoucherId)

                            claimableVoucherTemp = {
                                ...outletTaggableVoucher,

                                loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
                                batchId: 'claim',
                                batchIndex: -1,
                            };
                        }
                    }
                    else {
                        for (var i = 0; i < selectedOutletTaggableVouchersAll.length; i++) {
                            if (selectedOutletTaggableVouchersAll[i].uniqueId === currLoyaltyCampaign.taggableVoucherId) {
                            // only proceed if got this voucher tagged in loyalty campaign
            
                            // var promoTimeStart = moment().set({
                            //   hour: moment(selectedOutletTaggableVouchersAll[i].promoTimeStart).hour(),
                            //   minute: moment(selectedOutletTaggableVouchersAll[i].promoTimeStart).minute(),
                            // });
            
                            // var promoTimeEnd = moment().set({
                            //   hour: moment(selectedOutletTaggableVouchersAll[i].promoTimeEnd).hour(),
                            //   minute: moment(selectedOutletTaggableVouchersAll[i].promoTimeEnd).minute(),
                            // });
            
                            if (selectedOutlet && selectedOutlet.uniqueId &&
                                selectedOutletTaggableVouchersAll[i].voucherQuantity > 0
                                // &&
                                // moment().isSameOrAfter(selectedOutletTaggableVouchersAll[i].promoDateStart, 'day') &&
                                // moment().isSameOrBefore(selectedOutletTaggableVouchersAll[i].promoDateEnd, 'day') &&
                                // moment().isSameOrAfter(promoTimeStart) &&
                                // moment().isBefore(promoTimeEnd) 
                                &&
                                selectedOutletTaggableVouchersAll[i].isFreeToClaimVoucher) {
            
                                if (userEmail) {
                                const userTaggableVoucherSnapshot = await getDocs(
                                    query(
                                    collection(global.db, Collections.UserTaggableVoucher),
                                    where('outletId', '==', selectedOutlet.uniqueId),
                                    where('email', '==', userEmail),
                                    // where('redeemDate', '==', null),
                                    where('voucherId', '==', selectedOutletTaggableVouchersAll[i].uniqueId),
            
                                    // where('expirationDate', '>', moment().valueOf()),
                                    // orderBy('expirationDate'),
            
                                    // limit(selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser ? selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser : 1),
                                    limit(1), // always target for once (special case)
                                    )
                                );
            
                                var isValidToClaim = false;
                                if (userTaggableVoucherSnapshot) {
                                    if (
                                    // userTaggableVoucherSnapshot.size < selectedOutletTaggableVouchersAll[i].voucherMaxClaimPerUser
                                    userTaggableVoucherSnapshot.size > 0
                                    ) {
                                    isValidToClaim = false;
            
                                    // let userTaggableVoucherFound = userTaggableVoucherSnapshot.docs[0].data();
            
                                    // if (moment().isSame(userTaggableVoucherFound.createdAt, 'day')) {
                                    //   // this one should be the voucher that created just now (today), if paid online; just that the expiration date will set to yesterday (in case of user didn't paid online)
                                    //   // thus, can't claim first (will do a cron job to clear those unclaimed voucher due to unpaid online payments)
            
                                    //   isValidToClaim = false;
                                    // }
                                    // else {
                                    //   // means can still claim
                                    //   isValidToClaim = true;
                                    // }
                                    }
                                    else {
                                    isValidToClaim = true;
                                    }
                                }
            
                                if (isValidToClaim) {
                                    claimableVoucherTemp = {
                                    ...selectedOutletTaggableVouchersAll[i],
            
                                    loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
                                    batchId: 'claim',
                                    batchIndex: -1,
                                    };
                                    break;
                                }
                                }
                                else {
                                // since don't have email, we just assume this user havent claim yet (check again when later we get the user phone number to check)
            
                                claimableVoucherTemp = {
                                    ...selectedOutletTaggableVouchersAll[i],
            
                                    loyaltyCampaignId: currLoyaltyCampaign.uniqueId,
                                    batchId: 'claim',
                                    batchIndex: -1,
                                };
                                break;
                                }
                            }
                            }
                        }
                    }
                }

                if (claimableVoucherTemp !== null) {
                    break;
                }
            }
        }
        else {
        // already second or more visit, no need proceed
        }
    }

    return claimableVoucherTemp;
};